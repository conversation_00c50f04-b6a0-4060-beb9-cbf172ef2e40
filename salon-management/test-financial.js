// 测试财务管理功能的脚本
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://hynabcadepehsljpsyzd.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh5bmFiY2FkZXBlaHNsanBzeXpkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3NDY1MzIsImV4cCI6MjA2NjMyMjUzMn0.-C6EGu7CU1UbouowpNC2ra-OAQUMzCtcNvXuRzGUlzY'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testFinancialManagement() {
  console.log('开始测试财务管理功能...')
  
  // 1. 登录
  console.log('\n1. 用户登录...')
  const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'admin123456'
  })
  
  if (signInError) {
    console.error('登录失败:', signInError.message)
    return
  }
  
  console.log('登录成功:', signInData.user?.email)
  
  // 2. 查看费用分类
  console.log('\n2. 查看费用分类...')
  const { data: categories, error: categoriesError } = await supabase
    .from('expense_categories')
    .select('*')
    .order('name')
  
  if (categoriesError) {
    console.error('获取费用分类失败:', categoriesError.message)
  } else {
    console.log('费用分类:', categories?.length, '个')
    categories?.forEach((category, index) => {
      console.log(`  ${index + 1}. ${category.name} - ${category.description}`)
    })
  }
  
  // 3. 创建收入交易记录
  console.log('\n3. 创建收入交易记录...')
  const incomeTransaction = {
    transaction_type: 'income',
    category: '服务收入',
    amount: 288.00,
    description: '烫发造型服务收入',
    reference_type: 'service',
    payment_method: 'cash',
    created_by: signInData.user.id
  }
  
  const { data: newIncome, error: incomeError } = await supabase
    .from('financial_transactions')
    .insert(incomeTransaction)
    .select()
    .single()
  
  if (incomeError) {
    console.error('创建收入记录失败:', incomeError.message)
  } else {
    console.log('收入记录创建成功:', newIncome.category, '-', `¥${newIncome.amount}`)
  }
  
  // 4. 创建支出交易记录
  console.log('\n4. 创建支出交易记录...')
  const expenseTransaction = {
    transaction_type: 'expense',
    category: '产品采购',
    amount: 150.00,
    description: '购买洗发水和护发素',
    payment_method: 'card',
    created_by: signInData.user.id
  }
  
  const { data: newExpense, error: expenseError } = await supabase
    .from('financial_transactions')
    .insert(expenseTransaction)
    .select()
    .single()
  
  if (expenseError) {
    console.error('创建支出记录失败:', expenseError.message)
  } else {
    console.log('支出记录创建成功:', newExpense.category, '-', `¥${newExpense.amount}`)
  }
  
  // 5. 获取财务交易列表
  console.log('\n5. 获取财务交易列表...')
  const { data: transactions, error: transactionsError } = await supabase
    .from('financial_transactions')
    .select('*')
    .order('transaction_date', { ascending: false })
    .limit(10)
  
  if (transactionsError) {
    console.error('获取交易列表失败:', transactionsError.message)
  } else {
    console.log('交易记录:', transactions?.length, '条')
    transactions?.forEach((transaction, index) => {
      const sign = transaction.transaction_type === 'income' ? '+' : '-'
      console.log(`  ${index + 1}. ${transaction.category} ${sign}¥${transaction.amount} (${transaction.transaction_type})`)
    })
  }
  
  // 6. 计算财务统计
  console.log('\n6. 计算财务统计...')
  
  // 计算总收入
  const { data: incomeData } = await supabase
    .from('financial_transactions')
    .select('amount')
    .eq('transaction_type', 'income')
  
  const totalIncome = incomeData?.reduce((sum, item) => sum + parseFloat(item.amount), 0) || 0
  
  // 计算总支出
  const { data: expenseData } = await supabase
    .from('financial_transactions')
    .select('amount')
    .eq('transaction_type', 'expense')
  
  const totalExpense = expenseData?.reduce((sum, item) => sum + parseFloat(item.amount), 0) || 0
  
  // 计算利润
  const profit = totalIncome - totalExpense
  const profitMargin = totalIncome > 0 ? (profit / totalIncome * 100).toFixed(2) : 0
  
  console.log('财务统计:')
  console.log(`  总收入: ¥${totalIncome.toFixed(2)}`)
  console.log(`  总支出: ¥${totalExpense.toFixed(2)}`)
  console.log(`  净利润: ¥${profit.toFixed(2)}`)
  console.log(`  利润率: ${profitMargin}%`)
  
  // 7. 创建会员卡
  console.log('\n7. 创建会员卡...')
  
  // 获取一个客户
  const { data: customers } = await supabase
    .from('customers')
    .select('id, first_name, last_name')
    .limit(1)
  
  if (customers && customers.length > 0) {
    const customer = customers[0]
    const cardNumber = `MC${Date.now()}${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`
    
    const { data: newCard, error: cardError } = await supabase
      .from('membership_cards')
      .insert({
        customer_id: customer.id,
        card_number: cardNumber,
        card_type: 'gold',
        balance: 500.00,
        points: 100,
        discount_rate: 0.1
      })
      .select(`
        *,
        customers (first_name, last_name)
      `)
      .single()
    
    if (cardError) {
      console.error('创建会员卡失败:', cardError.message)
    } else {
      console.log('会员卡创建成功:')
      console.log(`  卡号: ${newCard.card_number}`)
      console.log(`  客户: ${newCard.customers?.first_name} ${newCard.customers?.last_name}`)
      console.log(`  余额: ¥${newCard.balance}`)
      console.log(`  积分: ${newCard.points}`)
      
      // 创建充值记录
      const { data: rechargeRecord, error: rechargeError } = await supabase
        .from('membership_transactions')
        .insert({
          card_id: newCard.id,
          transaction_type: 'recharge',
          amount: 500.00,
          balance_before: 0,
          balance_after: 500.00,
          points_before: 0,
          points_after: 100,
          description: '开卡充值',
          created_by: signInData.user.id
        })
        .select()
        .single()
      
      if (!rechargeError) {
        console.log('充值记录创建成功')
      }
    }
  }
  
  // 8. 获取会员卡列表
  console.log('\n8. 获取会员卡列表...')
  const { data: membershipCards, error: cardsError } = await supabase
    .from('membership_cards')
    .select(`
      *,
      customers (first_name, last_name, phone)
    `)
    .eq('is_active', true)
    .order('created_at', { ascending: false })
    .limit(5)
  
  if (cardsError) {
    console.error('获取会员卡列表失败:', cardsError.message)
  } else {
    console.log('会员卡列表:', membershipCards?.length, '张')
    membershipCards?.forEach((card, index) => {
      console.log(`  ${index + 1}. ${card.card_number} - ${card.customers?.first_name} ${card.customers?.last_name} (余额: ¥${card.balance})`)
    })
  }
  
  // 9. 按分类统计支出
  console.log('\n9. 按分类统计支出...')
  const { data: expenseByCategory } = await supabase
    .from('financial_transactions')
    .select('category, amount')
    .eq('transaction_type', 'expense')
  
  if (expenseByCategory) {
    const categoryStats = expenseByCategory.reduce((acc, item) => {
      acc[item.category] = (acc[item.category] || 0) + parseFloat(item.amount)
      return acc
    }, {})
    
    console.log('支出分类统计:')
    Object.entries(categoryStats).forEach(([category, amount]) => {
      console.log(`  ${category}: ¥${amount.toFixed(2)}`)
    })
  }
  
  // 10. 清理测试数据
  console.log('\n10. 清理测试数据...')
  
  // 删除测试交易记录
  if (newIncome) {
    await supabase.from('financial_transactions').delete().eq('id', newIncome.id)
  }
  if (newExpense) {
    await supabase.from('financial_transactions').delete().eq('id', newExpense.id)
  }
  
  // 删除测试会员卡
  const { data: testCards } = await supabase
    .from('membership_cards')
    .select('id')
    .like('card_number', 'MC%')
    .gte('created_at', new Date(Date.now() - 60000).toISOString()) // 最近1分钟创建的
  
  if (testCards && testCards.length > 0) {
    for (const card of testCards) {
      await supabase.from('membership_transactions').delete().eq('card_id', card.id)
      await supabase.from('membership_cards').delete().eq('id', card.id)
    }
    console.log('测试数据已清理')
  }
  
  console.log('\n测试完成!')
}

testFinancialManagement().catch(console.error)
