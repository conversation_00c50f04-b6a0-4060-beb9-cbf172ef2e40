// 测试客户管理API的脚本
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://hynabcadepehsljpsyzd.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh5bmFiY2FkZXBlaHNsanBzeXpkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3NDY1MzIsImV4cCI6MjA2NjMyMjUzMn0.-C6EGu7CU1UbouowpNC2ra-OAQUMzCtcNvXuRzGUlzY'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testCustomerManagement() {
  console.log('开始测试客户管理功能...')
  
  // 1. 登录
  console.log('\n1. 用户登录...')
  const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'admin123456'
  })
  
  if (signInError) {
    console.error('登录失败:', signInError.message)
    return
  }
  
  console.log('登录成功:', signInData.user?.email)
  
  // 2. 创建测试客户
  console.log('\n2. 创建测试客户...')
  const testCustomer = {
    first_name: '测试',
    last_name: '客户',
    phone: '13900000001',
    email: '<EMAIL>',
    date_of_birth: '1990-01-01',
    gender: 'female',
    address: '北京市朝阳区测试街道123号',
    notes: '这是一个测试客户',
    customer_type: 'regular'
  }
  
  const { data: newCustomer, error: createError } = await supabase
    .from('customers')
    .insert(testCustomer)
    .select()
    .single()
  
  if (createError) {
    console.error('创建客户失败:', createError.message)
  } else {
    console.log('客户创建成功:', newCustomer.first_name, newCustomer.last_name)
  }
  
  // 3. 获取客户列表
  console.log('\n3. 获取客户列表...')
  const { data: customers, error: listError } = await supabase
    .from('customers')
    .select('*')
    .order('created_at', { ascending: false })
    .limit(5)
  
  if (listError) {
    console.error('获取客户列表失败:', listError.message)
  } else {
    console.log('客户列表:', customers?.length, '条记录')
    customers?.forEach((customer, index) => {
      console.log(`  ${index + 1}. ${customer.first_name} ${customer.last_name} - ${customer.phone}`)
    })
  }
  
  // 4. 搜索客户
  console.log('\n4. 搜索客户...')
  const { data: searchResults, error: searchError } = await supabase
    .from('customers')
    .select('*')
    .or('first_name.ilike.%测试%,last_name.ilike.%测试%,phone.ilike.%测试%')
  
  if (searchError) {
    console.error('搜索客户失败:', searchError.message)
  } else {
    console.log('搜索结果:', searchResults?.length, '条记录')
  }
  
  // 5. 更新客户信息
  if (newCustomer) {
    console.log('\n5. 更新客户信息...')
    const { data: updatedCustomer, error: updateError } = await supabase
      .from('customers')
      .update({
        customer_type: 'vip',
        notes: '已升级为VIP客户'
      })
      .eq('id', newCustomer.id)
      .select()
      .single()
    
    if (updateError) {
      console.error('更新客户失败:', updateError.message)
    } else {
      console.log('客户更新成功:', updatedCustomer.customer_type)
    }
  }
  
  // 6. 测试仪表板统计
  console.log('\n6. 测试仪表板统计...')
  const { data: stats, error: statsError } = await supabase.rpc('get_dashboard_stats')
  
  if (statsError) {
    console.error('获取统计数据失败:', statsError.message)
  } else {
    console.log('统计数据:', {
      总客户数: stats.total_customers,
      总服务数: stats.total_services,
      今日预约: stats.today_appointments,
      本月营业额: stats.this_month_revenue
    })
  }
  
  // 7. 清理测试数据（可选）
  if (newCustomer) {
    console.log('\n7. 清理测试数据...')
    const { error: deleteError } = await supabase
      .from('customers')
      .delete()
      .eq('id', newCustomer.id)
    
    if (deleteError) {
      console.error('删除测试客户失败:', deleteError.message)
    } else {
      console.log('测试客户已删除')
    }
  }
  
  console.log('\n测试完成!')
}

testCustomerManagement().catch(console.error)
