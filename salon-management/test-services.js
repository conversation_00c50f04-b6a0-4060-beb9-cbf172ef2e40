// 测试服务项目管理功能的脚本
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://hynabcadepehsljpsyzd.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh5bmFiY2FkZXBlaHNsanBzeXpkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3NDY1MzIsImV4cCI6MjA2NjMyMjUzMn0.-C6EGu7CU1UbouowpNC2ra-OAQUMzCtcNvXuRzGUlzY'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testServiceManagement() {
  console.log('开始测试服务项目管理功能...')
  
  // 1. 登录
  console.log('\n1. 用户登录...')
  const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'admin123456'
  })
  
  if (signInError) {
    console.error('登录失败:', signInError.message)
    return
  }
  
  console.log('登录成功:', signInData.user?.email)
  
  // 2. 查看现有服务分类
  console.log('\n2. 查看现有服务分类...')
  const { data: categories, error: categoriesError } = await supabase
    .from('service_categories')
    .select('*')
    .order('display_order', { ascending: true })
  
  if (categoriesError) {
    console.error('获取服务分类失败:', categoriesError.message)
  } else {
    console.log('服务分类:', categories?.length, '个')
    categories?.forEach((category, index) => {
      console.log(`  ${index + 1}. ${category.name} - ${category.description}`)
    })
  }
  
  // 3. 查看现有服务项目
  console.log('\n3. 查看现有服务项目...')
  const { data: services, error: servicesError } = await supabase
    .from('services')
    .select(`
      *,
      service_categories (name)
    `)
    .order('created_at', { ascending: false })
    .limit(10)
  
  if (servicesError) {
    console.error('获取服务项目失败:', servicesError.message)
  } else {
    console.log('服务项目:', services?.length, '个')
    services?.forEach((service, index) => {
      console.log(`  ${index + 1}. ${service.name} - ¥${service.price} (${service.duration}分钟) [${service.service_categories?.name || service.category}]`)
    })
  }
  
  // 4. 创建测试服务项目
  console.log('\n4. 创建测试服务项目...')
  const testService = {
    name: '测试精剪服务',
    description: '这是一个测试的精剪服务项目',
    category_id: categories?.[0]?.id, // 使用第一个分类
    duration: 45,
    price: 88.00,
    vip_price: 78.00,
    cost: 20.00,
    required_skill_level: 3,
    tags: ['精剪', '造型', '测试'],
    preparation_time: 5,
    cleanup_time: 10
  }
  
  const { data: newService, error: createError } = await supabase
    .from('services')
    .insert(testService)
    .select(`
      *,
      service_categories (name)
    `)
    .single()
  
  if (createError) {
    console.error('创建服务失败:', createError.message)
  } else {
    console.log('服务创建成功:')
    console.log('  名称:', newService.name)
    console.log('  分类:', newService.service_categories?.name)
    console.log('  价格:', `¥${newService.price} (VIP: ¥${newService.vip_price})`)
    console.log('  时长:', `${newService.duration}分钟`)
    console.log('  标签:', newService.tags?.join(', '))
  }
  
  // 5. 搜索服务项目
  console.log('\n5. 搜索服务项目...')
  const { data: searchResults, error: searchError } = await supabase
    .from('services')
    .select(`
      *,
      service_categories (name)
    `)
    .or('name.ilike.%测试%,description.ilike.%测试%')
  
  if (searchError) {
    console.error('搜索服务失败:', searchError.message)
  } else {
    console.log('搜索结果:', searchResults?.length, '条记录')
  }
  
  // 6. 按分类过滤服务
  if (categories && categories.length > 0) {
    console.log('\n6. 按分类过滤服务...')
    const { data: categoryServices, error: filterError } = await supabase
      .from('services')
      .select(`
        *,
        service_categories (name)
      `)
      .eq('category_id', categories[0].id)
    
    if (filterError) {
      console.error('按分类过滤失败:', filterError.message)
    } else {
      console.log(`${categories[0].name} 分类的服务:`, categoryServices?.length, '个')
    }
  }
  
  // 7. 更新服务项目
  if (newService) {
    console.log('\n7. 更新服务项目...')
    const { data: updatedService, error: updateError } = await supabase
      .from('services')
      .update({
        price: 98.00,
        vip_price: 88.00,
        description: '这是一个更新后的测试精剪服务项目'
      })
      .eq('id', newService.id)
      .select()
      .single()
    
    if (updateError) {
      console.error('更新服务失败:', updateError.message)
    } else {
      console.log('服务更新成功:', updatedService.name, '- 新价格:', `¥${updatedService.price}`)
    }
  }
  
  // 8. 获取服务统计信息
  console.log('\n8. 获取服务统计信息...')
  
  // 按分类统计服务数量
  const categoryStats = await Promise.all(
    (categories || []).map(async (category) => {
      const { count } = await supabase
        .from('services')
        .select('*', { count: 'exact', head: true })
        .eq('category_id', category.id)
        .eq('is_active', true)
      
      return {
        category: category.name,
        count: count || 0
      }
    })
  )
  
  console.log('分类统计:')
  categoryStats.forEach(stat => {
    console.log(`  ${stat.category}: ${stat.count} 个服务`)
  })
  
  // 价格统计
  const { data: priceStats } = await supabase
    .from('services')
    .select('price')
    .eq('is_active', true)
  
  if (priceStats && priceStats.length > 0) {
    const prices = priceStats.map(s => parseFloat(s.price))
    const avgPrice = prices.reduce((a, b) => a + b, 0) / prices.length
    const minPrice = Math.min(...prices)
    const maxPrice = Math.max(...prices)
    
    console.log('价格统计:')
    console.log(`  平均价格: ¥${avgPrice.toFixed(2)}`)
    console.log(`  最低价格: ¥${minPrice}`)
    console.log(`  最高价格: ¥${maxPrice}`)
  }
  
  // 9. 清理测试数据
  if (newService) {
    console.log('\n9. 清理测试数据...')
    const { error: deleteError } = await supabase
      .from('services')
      .delete()
      .eq('id', newService.id)
    
    if (deleteError) {
      console.error('删除测试服务失败:', deleteError.message)
    } else {
      console.log('测试服务已删除')
    }
  }
  
  console.log('\n测试完成!')
}

testServiceManagement().catch(console.error)
