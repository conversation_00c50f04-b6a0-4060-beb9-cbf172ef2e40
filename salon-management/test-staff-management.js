// 测试员工管理功能的脚本
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://hynabcadepehsljpsyzd.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh5bmFiY2FkZXBlaHNsanBzeXpkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3NDY1MzIsImV4cCI6MjA2NjMyMjUzMn0.-C6EGu7CU1UbouowpNC2ra-OAQUMzCtcNvXuRzGUlzY'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testStaffManagement() {
  console.log('开始测试员工管理功能...')
  
  // 1. 登录
  console.log('\n1. 用户登录...')
  const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'admin123456'
  })
  
  if (signInError) {
    console.error('登录失败:', signInError.message)
    return
  }
  
  console.log('登录成功:', signInData.user?.email)
  
  // 2. 查看现有员工
  console.log('\n2. 查看现有员工...')
  const { data: existingStaff, error: listError } = await supabase
    .from('profiles')
    .select('*')
    .order('created_at', { ascending: false })
  
  if (listError) {
    console.error('获取员工列表失败:', listError.message)
  } else {
    console.log('现有员工:', existingStaff?.length, '人')
    existingStaff?.forEach((staff, index) => {
      console.log(`  ${index + 1}. ${staff.full_name || '未设置姓名'} (${staff.email}) - ${staff.role}`)
    })
  }
  
  // 3. 更新现有员工信息作为测试
  console.log('\n3. 更新现有员工信息作为测试...')
  let newStaff = null

  if (existingStaff && existingStaff.length > 0) {
    // 使用现有的第一个员工进行测试
    const { data: updatedStaff, error: updateError } = await supabase
      .from('profiles')
      .update({
        phone: '13900000002',
        hire_date: '2024-01-15',
        salary: 5000.00,
        commission_rate: 0.15
      })
      .eq('id', existingStaff[0].id)
      .select()
      .single()

    if (updateError) {
      console.error('更新员工失败:', updateError.message)
    } else {
      console.log('员工更新成功:', updatedStaff.full_name, '-', updatedStaff.role)
      newStaff = updatedStaff
    }
  } else {
    console.log('没有现有员工可以测试')
  }
  
  // 4. 创建排班记录
  if (newStaff) {
    console.log('\n4. 创建排班记录...')
    const today = new Date()
    const schedules = []
    
    // 创建未来7天的排班
    for (let i = 0; i < 7; i++) {
      const date = new Date(today)
      date.setDate(today.getDate() + i)
      
      schedules.push({
        staff_id: newStaff.id,
        date: date.toISOString().split('T')[0],
        start_time: '09:00',
        end_time: '18:00',
        break_start_time: '12:00',
        break_end_time: '13:00',
        is_available: true,
        created_by: signInData.user.id
      })
    }
    
    const { data: scheduleData, error: scheduleError } = await supabase
      .from('schedules')
      .insert(schedules)
      .select()
    
    if (scheduleError) {
      console.error('创建排班失败:', scheduleError.message)
    } else {
      console.log('排班创建成功:', scheduleData?.length, '条记录')
    }
  }
  
  // 5. 创建员工技能记录
  if (newStaff) {
    console.log('\n5. 创建员工技能记录...')
    
    // 获取一些服务项目
    const { data: services } = await supabase
      .from('services')
      .select('id, name')
      .limit(3)
    
    if (services && services.length > 0) {
      const skills = services.map((service, index) => ({
        staff_id: newStaff.id,
        service_id: service.id,
        skill_level: Math.min(5, 3 + index) // 技能等级3-5
      }))
      
      const { data: skillData, error: skillError } = await supabase
        .from('staff_skills')
        .insert(skills)
        .select()
      
      if (skillError) {
        console.error('创建技能记录失败:', skillError.message)
      } else {
        console.log('技能记录创建成功:', skillData?.length, '条记录')
        skillData?.forEach(skill => {
          const service = services.find(s => s.id === skill.service_id)
          console.log(`  - ${service?.name}: ${skill.skill_level}/5`)
        })
      }
    }
  }
  
  // 6. 搜索员工
  console.log('\n6. 搜索员工...')
  const { data: searchResults, error: searchError } = await supabase
    .from('profiles')
    .select('*')
    .or('full_name.ilike.%测试%,email.ilike.%test%')
  
  if (searchError) {
    console.error('搜索员工失败:', searchError.message)
  } else {
    console.log('搜索结果:', searchResults?.length, '条记录')
  }
  
  // 7. 再次更新员工信息
  if (newStaff) {
    console.log('\n7. 再次更新员工信息...')
    const { data: updatedStaff, error: updateError } = await supabase
      .from('profiles')
      .update({
        salary: 5500.00,
        commission_rate: 0.18
      })
      .eq('id', newStaff.id)
      .select()
      .single()

    if (updateError) {
      console.error('更新员工失败:', updateError.message)
    } else {
      console.log('员工更新成功:', updatedStaff.full_name, '- 新薪资:', updatedStaff.salary)
    }
  }
  
  // 8. 获取员工详细信息（包括技能和排班）
  if (newStaff) {
    console.log('\n8. 获取员工详细信息...')
    
    // 获取员工基本信息
    const { data: staffDetail } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', newStaff.id)
      .single()
    
    // 获取技能信息
    const { data: staffSkills } = await supabase
      .from('staff_skills')
      .select(`
        *,
        services (name, category)
      `)
      .eq('staff_id', newStaff.id)
    
    // 获取排班信息
    const { data: staffSchedules } = await supabase
      .from('schedules')
      .select('*')
      .eq('staff_id', newStaff.id)
      .order('date', { ascending: true })
    
    console.log('员工详情:')
    console.log('  基本信息:', staffDetail?.full_name, '-', staffDetail?.role)
    console.log('  技能数量:', staffSkills?.length || 0)
    console.log('  排班数量:', staffSchedules?.length || 0)
  }
  
  // 9. 清理测试数据（保留员工记录，只删除测试的排班和技能）
  if (newStaff) {
    console.log('\n9. 清理测试数据...')

    // 删除技能记录
    await supabase
      .from('staff_skills')
      .delete()
      .eq('staff_id', newStaff.id)

    // 删除排班记录
    await supabase
      .from('schedules')
      .delete()
      .eq('staff_id', newStaff.id)

    console.log('测试数据已清理（保留员工记录）')
  }
  
  console.log('\n测试完成!')
}

testStaffManagement().catch(console.error)
