-- 发廊管理系统数据库函数

-- 更新客户统计信息的函数
CREATE OR REPLACE FUNCTION update_customer_stats(customer_uuid UUID)
RETURNS void AS $$
BEGIN
  UPDATE customers 
  SET 
    total_spent = COALESCE((
      SELECT SUM(total) 
      FROM orders 
      WHERE customer_id = customer_uuid 
      AND payment_status = 'completed'
    ), 0),
    visit_count = COALESCE((
      SELECT COUNT(*) 
      FROM appointments 
      WHERE customer_id = customer_uuid 
      AND status = 'completed'
    ), 0),
    last_visit_date = (
      SELECT MAX(appointment_date) 
      FROM appointments 
      WHERE customer_id = customer_uuid 
      AND status = 'completed'
    )
  WHERE id = customer_uuid;
END;
$$ LANGUAGE plpgsql;

-- 获取仪表板统计数据的函数
CREATE OR REPLACE FUNCTION get_dashboard_stats()
RETURNS JSON AS $$
DECLARE
  result JSON;
BEGIN
  SELECT json_build_object(
    'total_customers', (SELECT COUNT(*) FROM customers),
    'total_services', (SELECT COUNT(*) FROM services WHERE is_active = true),
    'today_appointments', (
      SELECT COUNT(*) 
      FROM appointments 
      WHERE DATE(appointment_date) = CURRENT_DATE
    ),
    'this_month_revenue', COALESCE((
      SELECT SUM(total) 
      FROM orders 
      WHERE DATE_TRUNC('month', order_date) = DATE_TRUNC('month', CURRENT_DATE)
      AND payment_status = 'completed'
    ), 0),
    'pending_appointments', (
      SELECT COUNT(*) 
      FROM appointments 
      WHERE status = 'scheduled' 
      AND appointment_date >= NOW()
    ),
    'vip_customers', (
      SELECT COUNT(*) 
      FROM customers 
      WHERE customer_type = 'vip'
    )
  ) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 检查预约时间冲突的函数
CREATE OR REPLACE FUNCTION check_appointment_conflict(
  staff_uuid UUID,
  appointment_start TIMESTAMP WITH TIME ZONE,
  appointment_end TIMESTAMP WITH TIME ZONE,
  exclude_appointment_id UUID DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  conflict_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO conflict_count
  FROM appointments
  WHERE staff_id = staff_uuid
    AND status IN ('scheduled', 'in_progress')
    AND (exclude_appointment_id IS NULL OR id != exclude_appointment_id)
    AND (
      (appointment_date <= appointment_start AND appointment_date + INTERVAL '1 minute' * duration_minutes > appointment_start)
      OR
      (appointment_date < appointment_end AND appointment_date + INTERVAL '1 minute' * duration_minutes >= appointment_end)
      OR
      (appointment_date >= appointment_start AND appointment_date + INTERVAL '1 minute' * duration_minutes <= appointment_end)
    );
  
  RETURN conflict_count > 0;
END;
$$ LANGUAGE plpgsql;

-- 获取员工可用时间段的函数
CREATE OR REPLACE FUNCTION get_staff_availability(
  staff_uuid UUID,
  check_date DATE
)
RETURNS JSON AS $$
DECLARE
  schedule_record RECORD;
  appointments_json JSON;
  result JSON;
BEGIN
  -- 获取员工当天的排班信息
  SELECT * INTO schedule_record
  FROM schedules
  WHERE staff_id = staff_uuid AND date = check_date;
  
  -- 获取当天的预约信息
  SELECT json_agg(
    json_build_object(
      'start_time', appointment_date,
      'end_time', appointment_date + INTERVAL '1 minute' * duration_minutes,
      'status', status
    )
  ) INTO appointments_json
  FROM appointments
  WHERE staff_id = staff_uuid
    AND DATE(appointment_date) = check_date
    AND status IN ('scheduled', 'in_progress');
  
  SELECT json_build_object(
    'has_schedule', schedule_record IS NOT NULL,
    'schedule', CASE 
      WHEN schedule_record IS NOT NULL THEN
        json_build_object(
          'start_time', schedule_record.start_time,
          'end_time', schedule_record.end_time,
          'break_start', schedule_record.break_start_time,
          'break_end', schedule_record.break_end_time,
          'is_available', schedule_record.is_available
        )
      ELSE NULL
    END,
    'appointments', COALESCE(appointments_json, '[]'::json)
  ) INTO result;
  
  RETURN result;
END;
$$ LANGUAGE plpgsql;

-- 创建订单并更新客户统计的函数
CREATE OR REPLACE FUNCTION create_order_with_items(
  customer_uuid UUID,
  staff_uuid UUID,
  appointment_uuid UUID DEFAULT NULL,
  order_items JSON,
  payment_method_param payment_method DEFAULT 'cash',
  discount_amount DECIMAL(10,2) DEFAULT 0.00,
  notes_param TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  order_id UUID;
  item RECORD;
  subtotal_amount DECIMAL(10,2) := 0;
  total_amount DECIMAL(10,2);
BEGIN
  -- 创建订单
  INSERT INTO orders (
    customer_id, staff_id, appointment_id, 
    subtotal, discount, total, 
    payment_method, payment_status, notes
  ) VALUES (
    customer_uuid, staff_uuid, appointment_uuid,
    0, discount_amount, 0,
    payment_method_param, 'pending', notes_param
  ) RETURNING id INTO order_id;
  
  -- 添加订单项目并计算小计
  FOR item IN SELECT * FROM json_to_recordset(order_items) AS x(
    service_id UUID, quantity INTEGER, unit_price DECIMAL(10,2)
  )
  LOOP
    INSERT INTO order_items (order_id, service_id, quantity, unit_price, total_price)
    VALUES (order_id, item.service_id, item.quantity, item.unit_price, item.quantity * item.unit_price);
    
    subtotal_amount := subtotal_amount + (item.quantity * item.unit_price);
  END LOOP;
  
  -- 计算总金额
  total_amount := subtotal_amount - discount_amount;
  
  -- 更新订单总金额
  UPDATE orders 
  SET subtotal = subtotal_amount, total = total_amount
  WHERE id = order_id;
  
  -- 更新客户统计信息
  PERFORM update_customer_stats(customer_uuid);
  
  RETURN order_id;
END;
$$ LANGUAGE plpgsql;

-- 更新库存的函数
CREATE OR REPLACE FUNCTION update_product_stock(
  product_uuid UUID,
  quantity_change INTEGER,
  operation_type TEXT,
  reference_uuid UUID DEFAULT NULL,
  reason_text TEXT DEFAULT NULL,
  unit_cost_param DECIMAL(10,2) DEFAULT NULL,
  created_by_uuid UUID DEFAULT NULL
)
RETURNS void AS $$
DECLARE
  current_stock INTEGER;
  total_cost DECIMAL(10,2);
BEGIN
  -- 获取当前库存
  SELECT current_stock INTO current_stock FROM products WHERE id = product_uuid;
  
  -- 检查库存是否足够（对于出库操作）
  IF operation_type = 'out' AND current_stock + quantity_change < 0 THEN
    RAISE EXCEPTION '库存不足，当前库存：%，尝试减少：%', current_stock, ABS(quantity_change);
  END IF;
  
  -- 计算总成本
  total_cost := COALESCE(unit_cost_param, 0) * ABS(quantity_change);
  
  -- 更新产品库存
  UPDATE products 
  SET 
    current_stock = current_stock + quantity_change,
    updated_at = NOW()
  WHERE id = product_uuid;
  
  -- 记录库存变动
  INSERT INTO inventory_records (
    product_id, type, quantity, unit_cost, total_cost, 
    reason, reference_id, created_by
  ) VALUES (
    product_uuid, operation_type, quantity_change, unit_cost_param, total_cost,
    reason_text, reference_uuid, created_by_uuid
  );
END;
$$ LANGUAGE plpgsql;
