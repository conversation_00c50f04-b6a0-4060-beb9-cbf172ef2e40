// 测试预约管理功能的脚本
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://hynabcadepehsljpsyzd.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh5bmFiY2FkZXBlaHNsanBzeXpkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3NDY1MzIsImV4cCI6MjA2NjMyMjUzMn0.-C6EGu7CU1UbouowpNC2ra-OAQUMzCtcNvXuRzGUlzY'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testAppointmentManagement() {
  console.log('开始测试预约管理功能...')
  
  // 1. 登录
  console.log('\n1. 用户登录...')
  const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'admin123456'
  })
  
  if (signInError) {
    console.error('登录失败:', signInError.message)
    return
  }
  
  console.log('登录成功:', signInData.user?.email)
  
  // 2. 获取客户和员工数据
  console.log('\n2. 获取基础数据...')
  
  const { data: customers } = await supabase
    .from('customers')
    .select('id, first_name, last_name, phone')
    .limit(1)
  
  const { data: staff } = await supabase
    .from('profiles')
    .select('id, full_name, role')
    .eq('is_active', true)
    .limit(1)
  
  const { data: services } = await supabase
    .from('services')
    .select('id, name, price, duration')
    .eq('is_active', true)
    .limit(1)
  
  console.log('客户数量:', customers?.length || 0)
  console.log('员工数量:', staff?.length || 0)
  console.log('服务数量:', services?.length || 0)
  
  if (!customers?.length || !staff?.length || !services?.length) {
    console.log('缺少基础数据，无法创建预约')
    return
  }
  
  // 3. 创建测试预约
  console.log('\n3. 创建测试预约...')
  const tomorrow = new Date()
  tomorrow.setDate(tomorrow.getDate() + 1)
  const appointmentDate = tomorrow.toISOString().split('T')[0]
  
  const testAppointment = {
    customer_id: customers[0].id,
    staff_id: staff[0].id,
    service_id: services[0].id,
    appointment_date: appointmentDate,
    start_time: '10:00',
    end_time: '11:00',
    notes: '测试预约',
    duration_minutes: 60,
    price: services[0].price || 100,
    total_amount: services[0].price || 100,
    status: 'scheduled'
  }
  
  const { data: newAppointment, error: createError } = await supabase
    .from('appointments')
    .insert(testAppointment)
    .select(`
      *,
      customers (first_name, last_name, phone),
      profiles (full_name),
      services (name, price)
    `)
    .single()
  
  if (createError) {
    console.error('创建预约失败:', createError.message)
  } else {
    console.log('预约创建成功:')
    console.log('  客户:', newAppointment.customers?.first_name, newAppointment.customers?.last_name)
    console.log('  员工:', newAppointment.profiles?.full_name)
    console.log('  服务:', newAppointment.services?.name)
    console.log('  时间:', newAppointment.appointment_date, newAppointment.start_time, '-', newAppointment.end_time)
  }
  
  // 4. 获取预约列表
  console.log('\n4. 获取预约列表...')
  const { data: appointmentsList, error: listError } = await supabase
    .from('appointments')
    .select(`
      *,
      customers (first_name, last_name, phone),
      profiles (full_name),
      services (name, price)
    `)
    .order('appointment_date', { ascending: true })
    .limit(10)
  
  if (listError) {
    console.error('获取预约列表失败:', listError.message)
  } else {
    console.log('预约列表:', appointmentsList?.length, '条记录')
    appointmentsList?.forEach((apt, index) => {
      console.log(`  ${index + 1}. ${apt.customers?.first_name} ${apt.customers?.last_name} - ${apt.services?.name} (${apt.appointment_date} ${apt.start_time})`)
    })
  }
  
  // 5. 更新预约状态
  if (newAppointment) {
    console.log('\n5. 更新预约状态...')
    const { data: updatedAppointment, error: updateError } = await supabase
      .from('appointments')
      .update({
        status: 'in_progress',
        actual_start_time: new Date().toISOString(),
        notes: '测试预约 - 已开始服务'
      })
      .eq('id', newAppointment.id)
      .select()
      .single()
    
    if (updateError) {
      console.error('更新预约失败:', updateError.message)
    } else {
      console.log('预约状态更新成功:', updatedAppointment.status)
    }
  }
  
  // 6. 按日期查询预约
  console.log('\n6. 按日期查询预约...')
  const { data: todayAppointments, error: dateError } = await supabase
    .from('appointments')
    .select(`
      *,
      customers (first_name, last_name),
      profiles (full_name),
      services (name)
    `)
    .eq('appointment_date', appointmentDate)
  
  if (dateError) {
    console.error('按日期查询失败:', dateError.message)
  } else {
    console.log(`${appointmentDate} 的预约:`, todayAppointments?.length, '条')
  }
  
  // 7. 按员工查询预约
  console.log('\n7. 按员工查询预约...')
  const { data: staffAppointments, error: staffError } = await supabase
    .from('appointments')
    .select(`
      *,
      customers (first_name, last_name),
      services (name)
    `)
    .eq('staff_id', staff[0].id)
    .order('appointment_date', { ascending: true })
  
  if (staffError) {
    console.error('按员工查询失败:', staffError.message)
  } else {
    console.log(`${staff[0].full_name} 的预约:`, staffAppointments?.length, '条')
  }
  
  // 8. 检查时间冲突
  console.log('\n8. 检查时间冲突...')
  const { data: conflicts, error: conflictError } = await supabase
    .from('appointments')
    .select('id, start_time, end_time')
    .eq('staff_id', staff[0].id)
    .eq('appointment_date', appointmentDate)
    .in('status', ['scheduled', 'in_progress'])
    .or('start_time.lte.11:30,end_time.gte.10:30') // 检查10:30-11:30是否冲突
  
  if (conflictError) {
    console.error('检查冲突失败:', conflictError.message)
  } else {
    console.log('时间冲突检查:', conflicts?.length ? '有冲突' : '无冲突')
  }
  
  // 9. 完成预约
  if (newAppointment) {
    console.log('\n9. 完成预约...')
    const { data: completedAppointment, error: completeError } = await supabase
      .from('appointments')
      .update({
        status: 'completed',
        actual_end_time: new Date().toISOString()
      })
      .eq('id', newAppointment.id)
      .select()
      .single()
    
    if (completeError) {
      console.error('完成预约失败:', completeError.message)
    } else {
      console.log('预约已完成:', completedAppointment.status)
    }
  }
  
  // 10. 清理测试数据
  if (newAppointment) {
    console.log('\n10. 清理测试数据...')
    const { error: deleteError } = await supabase
      .from('appointments')
      .delete()
      .eq('id', newAppointment.id)
    
    if (deleteError) {
      console.error('删除测试预约失败:', deleteError.message)
    } else {
      console.log('测试预约已删除')
    }
  }
  
  console.log('\n测试完成!')
}

testAppointmentManagement().catch(console.error)
