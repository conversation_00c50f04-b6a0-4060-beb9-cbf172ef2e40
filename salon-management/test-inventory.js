// 测试库存管理功能的脚本
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://hynabcadepehsljpsyzd.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh5bmFiY2FkZXBlaHNsanBzeXpkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3NDY1MzIsImV4cCI6MjA2NjMyMjUzMn0.-C6EGu7CU1UbouowpNC2ra-OAQUMzCtcNvXuRzGUlzY'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testInventoryManagement() {
  console.log('开始测试库存管理功能...')
  
  // 1. 登录
  console.log('\n1. 用户登录...')
  const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'admin123456'
  })
  
  if (signInError) {
    console.error('登录失败:', signInError.message)
    return
  }
  
  console.log('登录成功:', signInData.user?.email)
  
  // 2. 查看产品分类
  console.log('\n2. 查看产品分类...')
  const { data: categories, error: categoriesError } = await supabase
    .from('product_categories')
    .select('*')
    .order('display_order')
  
  if (categoriesError) {
    console.error('获取产品分类失败:', categoriesError.message)
  } else {
    console.log('产品分类:', categories?.length, '个')
    categories?.forEach((category, index) => {
      console.log(`  ${index + 1}. ${category.name} - ${category.description}`)
    })
  }
  
  // 3. 查看供应商
  console.log('\n3. 查看供应商...')
  const { data: suppliers, error: suppliersError } = await supabase
    .from('suppliers')
    .select('*')
    .order('name')
  
  if (suppliersError) {
    console.error('获取供应商失败:', suppliersError.message)
  } else {
    console.log('供应商:', suppliers?.length, '个')
    suppliers?.forEach((supplier, index) => {
      console.log(`  ${index + 1}. ${supplier.name} - ${supplier.contact_person} (${supplier.phone})`)
    })
  }
  
  // 4. 创建测试产品
  console.log('\n4. 创建测试产品...')
  const testProduct = {
    name: '测试洗发水',
    sku: 'TEST-SHAMPOO-001',
    barcode: '1234567890123',
    category_id: categories?.[0]?.id, // 使用第一个分类
    brand: '测试品牌',
    description: '这是一个测试的洗发水产品',
    unit: '瓶',
    cost_price: 25.00,
    selling_price: 45.00,
    min_stock_level: 10,
    max_stock_level: 100,
    current_stock: 50
  }
  
  const { data: newProduct, error: createError } = await supabase
    .from('products')
    .insert(testProduct)
    .select(`
      *,
      product_categories (name)
    `)
    .single()
  
  if (createError) {
    console.error('创建产品失败:', createError.message)
  } else {
    console.log('产品创建成功:')
    console.log('  名称:', newProduct.name)
    console.log('  SKU:', newProduct.sku)
    console.log('  分类:', newProduct.product_categories?.name)
    console.log('  成本价:', `¥${newProduct.cost_price}`)
    console.log('  售价:', `¥${newProduct.selling_price}`)
    console.log('  当前库存:', newProduct.current_stock)
  }
  
  // 5. 创建库存流水记录
  if (newProduct) {
    console.log('\n5. 创建库存流水记录...')
    const { data: movement, error: movementError } = await supabase
      .from('inventory_movements')
      .insert({
        product_id: newProduct.id,
        movement_type: 'in',
        quantity: 50,
        unit_cost: 25.00,
        total_cost: 1250.00,
        reason: '初始库存',
        reference_type: 'initial',
        stock_before: 0,
        stock_after: 50,
        created_by: signInData.user.id
      })
      .select()
      .single()
    
    if (movementError) {
      console.error('创建库存流水失败:', movementError.message)
    } else {
      console.log('库存流水创建成功:', movement.reason, '-', `+${movement.quantity}`)
    }
  }
  
  // 6. 获取产品列表
  console.log('\n6. 获取产品列表...')
  const { data: products, error: productsError } = await supabase
    .from('products')
    .select(`
      *,
      product_categories (name)
    `)
    .order('created_at', { ascending: false })
    .limit(10)
  
  if (productsError) {
    console.error('获取产品列表失败:', productsError.message)
  } else {
    console.log('产品列表:', products?.length, '个')
    products?.forEach((product, index) => {
      console.log(`  ${index + 1}. ${product.name} (${product.sku}) - 库存:${product.current_stock} [${product.product_categories?.name}]`)
    })
  }
  
  // 7. 创建采购订单
  console.log('\n7. 创建采购订单...')
  const orderNumber = `PO${Date.now()}`
  
  const { data: purchaseOrder, error: poError } = await supabase
    .from('purchase_orders')
    .insert({
      order_number: orderNumber,
      supplier_id: suppliers?.[0]?.id,
      order_date: new Date().toISOString(),
      expected_delivery_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      status: 'pending',
      subtotal: 500.00,
      total_amount: 500.00,
      created_by: signInData.user.id
    })
    .select()
    .single()
  
  if (poError) {
    console.error('创建采购订单失败:', poError.message)
  } else {
    console.log('采购订单创建成功:', purchaseOrder.order_number, '-', `¥${purchaseOrder.total_amount}`)
    
    // 创建采购订单明细
    if (newProduct) {
      const { data: poItem, error: poItemError } = await supabase
        .from('purchase_order_items')
        .insert({
          purchase_order_id: purchaseOrder.id,
          product_id: newProduct.id,
          quantity: 20,
          unit_cost: 25.00
        })
        .select()
        .single()
      
      if (!poItemError) {
        console.log('采购订单明细创建成功:', `${poItem.quantity}个 x ¥${poItem.unit_cost}`)
      }
    }
  }
  
  // 8. 库存统计
  console.log('\n8. 库存统计...')
  
  // 产品总数
  const { count: totalProducts } = await supabase
    .from('products')
    .select('*', { count: 'exact', head: true })
    .eq('is_active', true)
  
  // 低库存产品
  const { data: lowStockProducts } = await supabase
    .from('products')
    .select('name, current_stock, min_stock_level')
    .eq('is_active', true)
    .lt('current_stock', 'min_stock_level')
  
  // 库存总价值
  const { data: inventoryValue } = await supabase
    .from('products')
    .select('current_stock, cost_price')
    .eq('is_active', true)
  
  let totalValue = 0
  if (inventoryValue) {
    totalValue = inventoryValue.reduce((sum, product) => {
      return sum + (product.current_stock * parseFloat(product.cost_price))
    }, 0)
  }
  
  console.log('库存统计:')
  console.log(`  产品总数: ${totalProducts || 0}`)
  console.log(`  低库存产品: ${lowStockProducts?.length || 0}`)
  console.log(`  库存总价值: ¥${totalValue.toFixed(2)}`)
  
  // 9. 按分类统计
  console.log('\n9. 按分类统计...')
  const categoryStats = await Promise.all(
    (categories || []).map(async (category) => {
      const { count } = await supabase
        .from('products')
        .select('*', { count: 'exact', head: true })
        .eq('category_id', category.id)
        .eq('is_active', true)
      
      return {
        category: category.name,
        count: count || 0
      }
    })
  )
  
  console.log('分类统计:')
  categoryStats.forEach(stat => {
    console.log(`  ${stat.category}: ${stat.count} 个产品`)
  })
  
  // 10. 获取库存变动记录
  console.log('\n10. 获取库存变动记录...')
  const { data: movements, error: movementsError } = await supabase
    .from('inventory_movements')
    .select(`
      *,
      products (name, sku)
    `)
    .order('created_at', { ascending: false })
    .limit(5)
  
  if (movementsError) {
    console.error('获取库存变动失败:', movementsError.message)
  } else {
    console.log('库存变动记录:', movements?.length, '条')
    movements?.forEach((movement, index) => {
      const sign = movement.movement_type === 'in' ? '+' : '-'
      console.log(`  ${index + 1}. ${movement.products?.name} ${sign}${movement.quantity} (${movement.reason})`)
    })
  }
  
  // 11. 清理测试数据
  console.log('\n11. 清理测试数据...')
  
  // 删除采购订单明细
  if (purchaseOrder) {
    await supabase.from('purchase_order_items').delete().eq('purchase_order_id', purchaseOrder.id)
    await supabase.from('purchase_orders').delete().eq('id', purchaseOrder.id)
  }
  
  // 删除库存流水
  if (newProduct) {
    await supabase.from('inventory_movements').delete().eq('product_id', newProduct.id)
    await supabase.from('products').delete().eq('id', newProduct.id)
  }
  
  console.log('测试数据已清理')
  
  console.log('\n测试完成!')
}

testInventoryManagement().catch(console.error)
