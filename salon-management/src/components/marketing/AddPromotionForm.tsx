'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Loader2 } from 'lucide-react'

interface AddPromotionFormProps {
  onSuccess?: (promotion: any) => void
  onCancel?: () => void
}

export default function AddPromotionForm({ onSuccess, onCancel }: AddPromotionFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    promotion_type: 'discount',
    discount_type: 'percentage',
    discount_value: '',
    min_purchase_amount: '0',
    max_discount_amount: '',
    start_date: '',
    end_date: '',
    usage_limit: '',
    customer_usage_limit: '1'
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const response = await fetch('/api/marketing/promotions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          discount_value: formData.discount_value ? parseFloat(formData.discount_value) : null,
          min_purchase_amount: parseFloat(formData.min_purchase_amount) || 0,
          max_discount_amount: formData.max_discount_amount ? parseFloat(formData.max_discount_amount) : null,
          usage_limit: formData.usage_limit ? parseInt(formData.usage_limit) : null,
          customer_usage_limit: parseInt(formData.customer_usage_limit) || 1
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || '创建促销活动失败')
      }

      if (onSuccess) {
        onSuccess(result.data)
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '创建促销活动失败')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const getPromotionTypeLabel = (type: string) => {
    const types = {
      'discount': '折扣优惠',
      'buy_one_get_one': '买一送一',
      'cashback': '现金返还',
      'points_multiplier': '积分倍增',
      'free_service': '免费服务'
    }
    return types[type as keyof typeof types] || type
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>新建促销活动</CardTitle>
        <CardDescription>
          创建新的促销活动来吸引客户
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* 基本信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">基本信息</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">活动名称 *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                  required
                  disabled={loading}
                  placeholder="例如：新客户8折优惠"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="promotion_type">活动类型 *</Label>
                <Select value={formData.promotion_type} onValueChange={(value) => handleChange('promotion_type', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="discount">折扣优惠</SelectItem>
                    <SelectItem value="buy_one_get_one">买一送一</SelectItem>
                    <SelectItem value="cashback">现金返还</SelectItem>
                    <SelectItem value="points_multiplier">积分倍增</SelectItem>
                    <SelectItem value="free_service">免费服务</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">活动描述</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleChange('description', e.target.value)}
                rows={3}
                disabled={loading}
                placeholder="详细描述活动内容和规则"
              />
            </div>
          </div>

          {/* 折扣设置 */}
          {formData.promotion_type === 'discount' && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">折扣设置</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="discount_type">折扣类型 *</Label>
                  <Select value={formData.discount_type} onValueChange={(value) => handleChange('discount_type', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="percentage">百分比折扣</SelectItem>
                      <SelectItem value="fixed_amount">固定金额</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="discount_value">
                    折扣值 * {formData.discount_type === 'percentage' ? '(%)' : '(元)'}
                  </Label>
                  <Input
                    id="discount_value"
                    type="number"
                    step="0.01"
                    min="0"
                    max={formData.discount_type === 'percentage' ? '100' : undefined}
                    value={formData.discount_value}
                    onChange={(e) => handleChange('discount_value', e.target.value)}
                    required
                    disabled={loading}
                    placeholder={formData.discount_type === 'percentage' ? '20' : '50'}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="max_discount_amount">最大优惠金额 (元)</Label>
                  <Input
                    id="max_discount_amount"
                    type="number"
                    step="0.01"
                    min="0"
                    value={formData.max_discount_amount}
                    onChange={(e) => handleChange('max_discount_amount', e.target.value)}
                    disabled={loading}
                    placeholder="不限制则留空"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="min_purchase_amount">最低消费金额 (元)</Label>
                <Input
                  id="min_purchase_amount"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.min_purchase_amount}
                  onChange={(e) => handleChange('min_purchase_amount', e.target.value)}
                  disabled={loading}
                />
              </div>
            </div>
          )}

          {/* 时间设置 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">时间设置</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="start_date">开始时间 *</Label>
                <Input
                  id="start_date"
                  type="datetime-local"
                  value={formData.start_date}
                  onChange={(e) => handleChange('start_date', e.target.value)}
                  required
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="end_date">结束时间 *</Label>
                <Input
                  id="end_date"
                  type="datetime-local"
                  value={formData.end_date}
                  onChange={(e) => handleChange('end_date', e.target.value)}
                  required
                  disabled={loading}
                />
              </div>
            </div>
          </div>

          {/* 使用限制 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">使用限制</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="usage_limit">总使用次数限制</Label>
                <Input
                  id="usage_limit"
                  type="number"
                  min="1"
                  value={formData.usage_limit}
                  onChange={(e) => handleChange('usage_limit', e.target.value)}
                  disabled={loading}
                  placeholder="不限制则留空"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="customer_usage_limit">单客户使用次数限制</Label>
                <Input
                  id="customer_usage_limit"
                  type="number"
                  min="1"
                  value={formData.customer_usage_limit}
                  onChange={(e) => handleChange('customer_usage_limit', e.target.value)}
                  disabled={loading}
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel} disabled={loading}>
                取消
              </Button>
            )}
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              创建活动
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
