'use client'

import { cn } from '@/lib/utils'
import { Loader2, Scissors } from 'lucide-react'

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg'
  text?: string
  className?: string
  variant?: 'default' | 'minimal' | 'branded'
}

export function Loading({ 
  size = 'md', 
  text = '加载中...', 
  className,
  variant = 'default'
}: LoadingProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12'
  }

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  }

  if (variant === 'minimal') {
    return (
      <div className={cn('flex items-center justify-center', className)}>
        <Loader2 className={cn('animate-spin text-blue-600', sizeClasses[size])} />
      </div>
    )
  }

  if (variant === 'branded') {
    return (
      <div className={cn('flex flex-col items-center justify-center space-y-4', className)}>
        <div className="relative">
          <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center animate-pulse">
            <Scissors className="h-8 w-8 text-white" />
          </div>
          <div className="absolute inset-0 w-16 h-16 border-4 border-blue-200 rounded-full animate-spin border-t-blue-600"></div>
        </div>
        <div className="text-center">
          <p className="text-lg font-medium text-slate-900">发廊管理系统</p>
          <p className="text-sm text-slate-500 mt-1">{text}</p>
        </div>
      </div>
    )
  }

  return (
    <div className={cn('flex flex-col items-center justify-center space-y-3', className)}>
      <Loader2 className={cn('animate-spin text-blue-600', sizeClasses[size])} />
      <p className={cn('text-slate-600', textSizeClasses[size])}>{text}</p>
    </div>
  )
}

export function PageLoading({ text = '页面加载中...' }: { text?: string }) {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-white">
      <Loading variant="branded" text={text} />
    </div>
  )
}

export function CardLoading({ text = '数据加载中...' }: { text?: string }) {
  return (
    <div className="flex items-center justify-center py-12">
      <Loading size="md" text={text} />
    </div>
  )
}

export function ButtonLoading({ size = 'sm' }: { size?: 'sm' | 'md' | 'lg' }) {
  return <Loader2 className={cn('animate-spin', sizeClasses[size])} />
}

const sizeClasses = {
  sm: 'h-4 w-4',
  md: 'h-8 w-8',
  lg: 'h-12 w-12'
}
