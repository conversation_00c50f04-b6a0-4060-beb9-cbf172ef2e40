'use client'

import * as React from 'react'
import { Search, X } from 'lucide-react'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface SearchProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
  onClear?: () => void
  debounceMs?: number
}

export function SearchInput({ 
  value, 
  onChange, 
  placeholder = '搜索...', 
  className,
  onClear,
  debounceMs = 300
}: SearchProps) {
  const [localValue, setLocalValue] = React.useState(value)
  const timeoutRef = React.useRef<NodeJS.Timeout>()

  React.useEffect(() => {
    setLocalValue(value)
  }, [value])

  React.useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    timeoutRef.current = setTimeout(() => {
      onChange(localValue)
    }, debounceMs)

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [localValue, onChange, debounceMs])

  const handleClear = () => {
    setLocalValue('')
    onChange('')
    onClear?.()
  }

  return (
    <div className={cn('relative', className)}>
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
      <Input
        value={localValue}
        onChange={(e) => setLocalValue(e.target.value)}
        placeholder={placeholder}
        className="pl-10 pr-10"
      />
      {localValue && (
        <Button
          variant="ghost"
          size="sm"
          className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 hover:bg-slate-100"
          onClick={handleClear}
        >
          <X className="h-3 w-3" />
        </Button>
      )}
    </div>
  )
}

interface QuickSearchProps {
  suggestions?: string[]
  onSuggestionClick?: (suggestion: string) => void
  recentSearches?: string[]
  onRecentSearchClick?: (search: string) => void
}

export function QuickSearch({
  suggestions = [],
  onSuggestionClick,
  recentSearches = [],
  onRecentSearchClick
}: QuickSearchProps) {
  return (
    <div className="space-y-4">
      {suggestions.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-slate-700 mb-2">建议搜索</h4>
          <div className="flex flex-wrap gap-2">
            {suggestions.map((suggestion, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                className="text-xs"
                onClick={() => onSuggestionClick?.(suggestion)}
              >
                {suggestion}
              </Button>
            ))}
          </div>
        </div>
      )}

      {recentSearches.length > 0 && (
        <div>
          <h4 className="text-sm font-medium text-slate-700 mb-2">最近搜索</h4>
          <div className="space-y-1">
            {recentSearches.map((search, index) => (
              <Button
                key={index}
                variant="ghost"
                size="sm"
                className="w-full justify-start text-xs text-slate-600"
                onClick={() => onRecentSearchClick?.(search)}
              >
                <Search className="mr-2 h-3 w-3" />
                {search}
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
