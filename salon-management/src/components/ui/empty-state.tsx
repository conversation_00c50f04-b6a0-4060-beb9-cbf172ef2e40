'use client'

import { But<PERSON> } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { LucideIcon } from 'lucide-react'

interface EmptyStateProps {
  icon: LucideIcon
  title: string
  description?: string
  action?: {
    label: string
    onClick: () => void
  }
  className?: string
}

export function EmptyState({ 
  icon: Icon, 
  title, 
  description, 
  action, 
  className 
}: EmptyStateProps) {
  return (
    <div className={cn(
      'flex flex-col items-center justify-center py-12 px-4 text-center',
      className
    )}>
      <div className="w-16 h-16 bg-gradient-to-br from-slate-100 to-slate-200 rounded-full flex items-center justify-center mb-4">
        <Icon className="h-8 w-8 text-slate-400" />
      </div>
      <h3 className="text-lg font-semibold text-slate-900 mb-2">{title}</h3>
      {description && (
        <p className="text-sm text-slate-500 mb-6 max-w-md">{description}</p>
      )}
      {action && (
        <Button onClick={action.onClick} className="mt-2">
          {action.label}
        </Button>
      )}
    </div>
  )
}

export function NoDataFound({ 
  title = '暂无数据', 
  description = '当前没有找到任何数据',
  className 
}: { 
  title?: string
  description?: string
  className?: string 
}) {
  return (
    <div className={cn(
      'flex flex-col items-center justify-center py-8 px-4 text-center',
      className
    )}>
      <div className="w-12 h-12 bg-slate-100 rounded-full flex items-center justify-center mb-3">
        <div className="w-6 h-6 border-2 border-slate-300 rounded border-dashed"></div>
      </div>
      <h4 className="text-base font-medium text-slate-700 mb-1">{title}</h4>
      <p className="text-sm text-slate-500">{description}</p>
    </div>
  )
}
