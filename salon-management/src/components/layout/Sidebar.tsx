'use client'

import { useRouter, usePathname } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'
import {
  Home,
  Users,
  Calendar,
  Scissors,
  DollarSign,
  Package,
  TrendingUp,
  Settings,
  LogOut,
  UserCheck,
  Megaphone
} from 'lucide-react'
import { useAuth } from '@/contexts/AuthContext'

const navigation = [
  {
    name: '仪表板',
    href: '/dashboard',
    icon: Home,
    description: '总览数据'
  },
  {
    name: '客户管理',
    href: '/dashboard/customers',
    icon: Users,
    description: '客户档案'
  },
  {
    name: '员工管理',
    href: '/dashboard/staff',
    icon: UserCheck,
    description: '员工档案'
  },
  {
    name: '预约管理',
    href: '/dashboard/appointments',
    icon: Calendar,
    description: '预约安排'
  },
  {
    name: '服务项目',
    href: '/dashboard/services',
    icon: Scissors,
    description: '服务管理'
  },
  {
    name: '财务管理',
    href: '/dashboard/financial',
    icon: DollarSign,
    description: '收支管理'
  },
  {
    name: '库存管理',
    href: '/dashboard/inventory',
    icon: Package,
    description: '库存控制'
  },
  {
    name: '营销管理',
    href: '/dashboard/marketing',
    icon: Megaphone,
    description: '营销活动'
  },
  {
    name: '系统设置',
    href: '/dashboard/settings',
    icon: Settings,
    description: '系统配置'
  },
]

export default function Sidebar() {
  const router = useRouter()
  const pathname = usePathname()
  const { user, signOut } = useAuth()

  const handleSignOut = async () => {
    await signOut()
    router.push('/login')
  }

  return (
    <div className="flex flex-col h-full bg-gradient-to-b from-slate-50 to-white border-r border-slate-200 shadow-sm">
      {/* Logo */}
      <div className="flex items-center justify-center h-16 px-4 border-b border-slate-200 bg-white/80 backdrop-blur-sm">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
            <Scissors className="h-4 w-4 text-white" />
          </div>
          <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            发廊管理
          </h1>
        </div>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-3 py-6 space-y-1">
        {navigation.map((item) => {
          const isActive = pathname === item.href || pathname.startsWith(item.href + '/')
          return (
            <div key={item.name} className="group">
              <Button
                variant={isActive ? 'default' : 'ghost'}
                className={cn(
                  'w-full justify-start h-12 px-3 transition-all duration-200',
                  isActive
                    ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-md hover:shadow-lg'
                    : 'hover:bg-slate-100 hover:shadow-sm text-slate-700 hover:text-slate-900'
                )}
                onClick={() => router.push(item.href)}
              >
                <item.icon className={cn(
                  "mr-3 h-5 w-5 transition-transform duration-200",
                  isActive ? "text-white" : "text-slate-500 group-hover:text-slate-700",
                  "group-hover:scale-110"
                )} />
                <div className="flex flex-col items-start">
                  <span className="font-medium">{item.name}</span>
                  <span className={cn(
                    "text-xs opacity-70",
                    isActive ? "text-white/80" : "text-slate-500"
                  )}>
                    {item.description}
                  </span>
                </div>
              </Button>
            </div>
          )
        })}
      </nav>

      {/* User info and logout */}
      <div className="p-4 border-t border-slate-200 bg-white/50 backdrop-blur-sm">
        <div className="flex items-center mb-3 p-3 rounded-lg bg-white/80 border border-slate-200">
          <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-3">
            <span className="text-white font-semibold text-sm">
              {user?.email?.charAt(0).toUpperCase()}
            </span>
          </div>
          <div className="flex-1">
            <p className="text-sm font-medium text-slate-900 truncate">
              {user?.email}
            </p>
            <div className="flex items-center space-x-1">
              <Badge variant="secondary" className="text-xs">
                管理员
              </Badge>
            </div>
          </div>
        </div>
        <Button
          variant="outline"
          className="w-full justify-start hover:bg-red-50 hover:text-red-600 hover:border-red-200 transition-colors duration-200"
          onClick={handleSignOut}
        >
          <LogOut className="mr-3 h-4 w-4" />
          退出登录
        </Button>
      </div>
    </div>
  )
}
