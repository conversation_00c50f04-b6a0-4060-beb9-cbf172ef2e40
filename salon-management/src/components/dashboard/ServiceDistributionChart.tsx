'use client'

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Legend } from 'recharts'

// 模拟数据
const data = [
  { name: '洗剪吹', value: 35, color: '#0088FE' },
  { name: '染发', value: 25, color: '#00C49F' },
  { name: '烫发', value: 20, color: '#FFBB28' },
  { name: '护理', value: 15, color: '#FF8042' },
  { name: '其他', value: 5, color: '#8884D8' },
]

export default function ServiceDistributionChart() {
  return (
    <div className="h-80">
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip formatter={(value) => [`${value}%`, '占比']} />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </div>
  )
}
