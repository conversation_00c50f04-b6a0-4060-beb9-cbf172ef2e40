'use client'

import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'

// 模拟数据 - 在实际应用中这些数据应该从API获取
const data = [
  { name: '周一', revenue: 1200, appointments: 8 },
  { name: '周二', revenue: 1800, appointments: 12 },
  { name: '周三', revenue: 2200, appointments: 15 },
  { name: '周四', revenue: 1600, appointments: 10 },
  { name: '周五', revenue: 2800, appointments: 18 },
  { name: '周六', revenue: 3200, appointments: 22 },
  { name: '周日', revenue: 2400, appointments: 16 },
]

export default function RevenueChart() {
  return (
    <div className="h-80">
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip 
            formatter={(value, name) => [
              name === 'revenue' ? `¥${value}` : value,
              name === 'revenue' ? '营业额' : '预约数'
            ]}
          />
          <Line 
            type="monotone" 
            dataKey="revenue" 
            stroke="#8884d8" 
            strokeWidth={2}
            dot={{ fill: '#8884d8' }}
          />
          <Line 
            type="monotone" 
            dataKey="appointments" 
            stroke="#82ca9d" 
            strokeWidth={2}
            dot={{ fill: '#82ca9d' }}
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  )
}
