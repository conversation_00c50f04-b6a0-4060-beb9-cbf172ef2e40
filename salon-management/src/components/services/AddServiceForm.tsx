'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Loader2, Plus, X } from 'lucide-react'
import { createSupabaseClient } from '@/lib/supabase'

interface AddServiceFormProps {
  onSuccess?: (service: any) => void
  onCancel?: () => void
}

export default function AddServiceForm({ onSuccess, onCancel }: AddServiceFormProps) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category_id: '',
    duration: '',
    price: '',
    vip_price: '',
    cost: '',
    required_skill_level: '1',
    image_url: '',
    preparation_time: '',
    cleanup_time: ''
  })
  const [tags, setTags] = useState<string[]>([])
  const [newTag, setNewTag] = useState('')
  const [categories, setCategories] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const supabase = createSupabaseClient()

  useEffect(() => {
    fetchCategories()
  }, [])

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('service_categories')
        .select('*')
        .eq('is_active', true)
        .order('display_order', { ascending: true })

      if (!error) {
        setCategories(data || [])
      }
    } catch (error) {
      console.error('获取分类失败:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const response = await fetch('/api/services', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          duration: parseInt(formData.duration),
          price: parseFloat(formData.price),
          vip_price: formData.vip_price ? parseFloat(formData.vip_price) : null,
          cost: formData.cost ? parseFloat(formData.cost) : 0,
          required_skill_level: parseInt(formData.required_skill_level),
          preparation_time: formData.preparation_time ? parseInt(formData.preparation_time) : 0,
          cleanup_time: formData.cleanup_time ? parseInt(formData.cleanup_time) : 0,
          tags
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || '创建服务失败')
      }

      if (onSuccess) {
        onSuccess(result.data)
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '创建服务失败')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()])
      setNewTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove))
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addTag()
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>新增服务项目</CardTitle>
        <CardDescription>
          添加新的服务项目到系统中
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* 基本信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">基本信息</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">服务名称 *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleChange('name', e.target.value)}
                  required
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="category_id">服务分类</Label>
                <Select value={formData.category_id} onValueChange={(value) => handleChange('category_id', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择分类" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">服务描述</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleChange('description', e.target.value)}
                rows={3}
                disabled={loading}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="image_url">服务图片URL</Label>
              <Input
                id="image_url"
                type="url"
                value={formData.image_url}
                onChange={(e) => handleChange('image_url', e.target.value)}
                disabled={loading}
                placeholder="https://example.com/image.jpg"
              />
            </div>
          </div>

          {/* 时间和价格 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">时间和价格</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="duration">服务时长 (分钟) *</Label>
                <Input
                  id="duration"
                  type="number"
                  min="1"
                  value={formData.duration}
                  onChange={(e) => handleChange('duration', e.target.value)}
                  required
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="preparation_time">准备时间 (分钟)</Label>
                <Input
                  id="preparation_time"
                  type="number"
                  min="0"
                  value={formData.preparation_time}
                  onChange={(e) => handleChange('preparation_time', e.target.value)}
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="cleanup_time">清理时间 (分钟)</Label>
                <Input
                  id="cleanup_time"
                  type="number"
                  min="0"
                  value={formData.cleanup_time}
                  onChange={(e) => handleChange('cleanup_time', e.target.value)}
                  disabled={loading}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="price">标准价格 *</Label>
                <Input
                  id="price"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.price}
                  onChange={(e) => handleChange('price', e.target.value)}
                  required
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="vip_price">VIP价格</Label>
                <Input
                  id="vip_price"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.vip_price}
                  onChange={(e) => handleChange('vip_price', e.target.value)}
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="cost">成本</Label>
                <Input
                  id="cost"
                  type="number"
                  step="0.01"
                  min="0"
                  value={formData.cost}
                  onChange={(e) => handleChange('cost', e.target.value)}
                  disabled={loading}
                />
              </div>
            </div>
          </div>

          {/* 技能要求和标签 */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">技能要求和标签</h3>
            
            <div className="space-y-2">
              <Label htmlFor="required_skill_level">技能要求</Label>
              <Select value={formData.required_skill_level} onValueChange={(value) => handleChange('required_skill_level', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">初级</SelectItem>
                  <SelectItem value="2">中级</SelectItem>
                  <SelectItem value="3">高级</SelectItem>
                  <SelectItem value="4">专家</SelectItem>
                  <SelectItem value="5">大师</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>服务标签</Label>
              <div className="flex space-x-2">
                <Input
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="输入标签后按回车添加"
                  disabled={loading}
                />
                <Button type="button" onClick={addTag} disabled={loading || !newTag.trim()}>
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              {tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-2">
                  {tags.map((tag, index) => (
                    <div key={index} className="flex items-center bg-blue-100 text-blue-800 px-2 py-1 rounded-md text-sm">
                      <span>{tag}</span>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="ml-1 h-4 w-4 p-0"
                        onClick={() => removeTag(tag)}
                        disabled={loading}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel} disabled={loading}>
                取消
              </Button>
            )}
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              创建服务
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
