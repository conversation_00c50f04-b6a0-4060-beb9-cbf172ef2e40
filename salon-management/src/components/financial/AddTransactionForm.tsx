'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Loader2 } from 'lucide-react'
import { createSupabaseClient } from '@/lib/supabase'

interface AddTransactionFormProps {
  onSuccess?: (transaction: any) => void
  onCancel?: () => void
}

export default function AddTransactionForm({ onSuccess, onCancel }: AddTransactionFormProps) {
  const [formData, setFormData] = useState({
    transaction_type: 'income',
    category: '',
    amount: '',
    description: '',
    payment_method: 'cash',
    transaction_date: new Date().toISOString().split('T')[0]
  })
  const [categories, setCategories] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const supabase = createSupabaseClient()

  useEffect(() => {
    fetchCategories()
  }, [formData.transaction_type])

  const fetchCategories = async () => {
    try {
      if (formData.transaction_type === 'expense') {
        // 获取费用分类
        const { data, error } = await supabase
          .from('expense_categories')
          .select('name')
          .eq('is_active', true)
          .order('name')

        if (!error && data) {
          setCategories(data.map(cat => cat.name))
        }
      } else {
        // 收入分类
        setCategories([
          '服务收入',
          '产品销售',
          '会员充值',
          '其他收入'
        ])
      }
    } catch (error) {
      console.error('获取分类失败:', error)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const response = await fetch('/api/financial/transactions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          amount: parseFloat(formData.amount),
          transaction_date: new Date(formData.transaction_date).toISOString()
        }),
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || '创建交易记录失败')
      }

      if (onSuccess) {
        onSuccess(result.data)
      }
    } catch (error) {
      setError(error instanceof Error ? error.message : '创建交易记录失败')
    } finally {
      setLoading(false)
    }
  }

  const handleChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>记录财务交易</CardTitle>
        <CardDescription>
          添加收入或支出记录
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="transaction_type">交易类型 *</Label>
              <Select value={formData.transaction_type} onValueChange={(value) => handleChange('transaction_type', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="income">收入</SelectItem>
                  <SelectItem value="expense">支出</SelectItem>
                  <SelectItem value="refund">退款</SelectItem>
                  <SelectItem value="commission">提成</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">分类 *</Label>
              <Select value={formData.category} onValueChange={(value) => handleChange('category', value)}>
                <SelectTrigger>
                  <SelectValue placeholder="选择分类" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="amount">金额 *</Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                min="0"
                value={formData.amount}
                onChange={(e) => handleChange('amount', e.target.value)}
                required
                disabled={loading}
                placeholder="0.00"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="payment_method">支付方式</Label>
              <Select value={formData.payment_method} onValueChange={(value) => handleChange('payment_method', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="cash">现金</SelectItem>
                  <SelectItem value="card">银行卡</SelectItem>
                  <SelectItem value="mobile_payment">移动支付</SelectItem>
                  <SelectItem value="membership_card">会员卡</SelectItem>
                  <SelectItem value="other">其他</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="transaction_date">交易日期</Label>
            <Input
              id="transaction_date"
              type="date"
              value={formData.transaction_date}
              onChange={(e) => handleChange('transaction_date', e.target.value)}
              disabled={loading}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">描述</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleChange('description', e.target.value)}
              disabled={loading}
              placeholder="交易描述（可选）"
              rows={3}
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel} disabled={loading}>
                取消
              </Button>
            )}
            <Button type="submit" disabled={loading}>
              {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              保存记录
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
