import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

// GET /api/service-categories - 获取服务分类列表
export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const isActive = searchParams.get('is_active')
    const withCount = searchParams.get('with_count') === 'true'

    let query = supabase
      .from('service_categories')
      .select('*')
      .order('display_order', { ascending: true })

    // 状态过滤
    if (isActive !== null && isActive !== '') {
      query = query.eq('is_active', isActive === 'true')
    }

    const { data: categories, error } = await query

    if (error) {
      console.error('获取服务分类失败:', error)
      return NextResponse.json({ error: '获取服务分类失败' }, { status: 500 })
    }

    // 如果需要统计每个分类下的服务数量
    if (withCount && categories) {
      const categoriesWithCount = await Promise.all(
        categories.map(async (category) => {
          const { count } = await supabase
            .from('services')
            .select('*', { count: 'exact', head: true })
            .eq('category_id', category.id)
            .eq('is_active', true)

          return {
            ...category,
            service_count: count || 0
          }
        })
      )

      return NextResponse.json({ data: categoriesWithCount })
    }

    return NextResponse.json({ data: categories })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}

// POST /api/service-categories - 创建新服务分类
export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const body = await request.json()
    const {
      name,
      description,
      display_order = 0
    } = body

    // 验证必填字段
    if (!name) {
      return NextResponse.json({ error: '分类名称是必填字段' }, { status: 400 })
    }

    // 检查分类名称是否已存在
    const { data: existingCategory } = await supabase
      .from('service_categories')
      .select('id')
      .eq('name', name)
      .single()

    if (existingCategory) {
      return NextResponse.json({ error: '该分类名称已存在' }, { status: 400 })
    }

    // 创建服务分类
    const { data, error } = await supabase
      .from('service_categories')
      .insert({
        name,
        description,
        display_order
      })
      .select()
      .single()

    if (error) {
      console.error('创建服务分类失败:', error)
      return NextResponse.json({ error: '创建服务分类失败' }, { status: 500 })
    }

    return NextResponse.json({ data }, { status: 201 })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
