import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

// GET /api/membership/cards - 获取会员卡列表
export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search') || ''
    const cardType = searchParams.get('card_type') || ''
    const isActive = searchParams.get('is_active')

    let query = supabase
      .from('membership_cards')
      .select(`
        *,
        customers (id, first_name, last_name, phone, email)
      `, { count: 'exact' })
      .order('created_at', { ascending: false })

    // 搜索过滤
    if (search) {
      query = query.or(`card_number.ilike.%${search}%,customers.first_name.ilike.%${search}%,customers.last_name.ilike.%${search}%,customers.phone.ilike.%${search}%`)
    }

    // 卡类型过滤
    if (cardType) {
      query = query.eq('card_type', cardType)
    }

    // 状态过滤
    if (isActive !== null && isActive !== '') {
      query = query.eq('is_active', isActive === 'true')
    }

    // 分页
    const from = (page - 1) * limit
    const to = from + limit - 1
    query = query.range(from, to)

    const { data, error, count } = await query

    if (error) {
      console.error('获取会员卡列表失败:', error)
      return NextResponse.json({ error: '获取会员卡列表失败' }, { status: 500 })
    }

    return NextResponse.json({
      data,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}

// POST /api/membership/cards - 创建新会员卡
export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const body = await request.json()
    const {
      customer_id,
      card_type = 'standard',
      balance = 0,
      points = 0,
      discount_rate = 0,
      expiry_date
    } = body

    // 验证必填字段
    if (!customer_id) {
      return NextResponse.json({ error: '客户ID是必填字段' }, { status: 400 })
    }

    // 检查客户是否已有会员卡
    const { data: existingCard } = await supabase
      .from('membership_cards')
      .select('id')
      .eq('customer_id', customer_id)
      .eq('is_active', true)
      .single()

    if (existingCard) {
      return NextResponse.json({ error: '该客户已有有效的会员卡' }, { status: 400 })
    }

    // 生成卡号
    const cardNumber = `MC${Date.now()}${Math.floor(Math.random() * 1000).toString().padStart(3, '0')}`

    // 创建会员卡
    const { data, error } = await supabase
      .from('membership_cards')
      .insert({
        customer_id,
        card_number: cardNumber,
        card_type,
        balance: parseFloat(balance),
        points: parseInt(points),
        discount_rate: parseFloat(discount_rate),
        expiry_date
      })
      .select(`
        *,
        customers (id, first_name, last_name, phone, email)
      `)
      .single()

    if (error) {
      console.error('创建会员卡失败:', error)
      return NextResponse.json({ error: '创建会员卡失败' }, { status: 500 })
    }

    // 如果有初始余额，创建充值记录
    if (parseFloat(balance) > 0) {
      await supabase
        .from('membership_transactions')
        .insert({
          card_id: data.id,
          transaction_type: 'recharge',
          amount: parseFloat(balance),
          balance_before: 0,
          balance_after: parseFloat(balance),
          points_before: 0,
          points_after: parseInt(points),
          description: '开卡充值',
          created_by: session.user.id
        })
    }

    return NextResponse.json({ data }, { status: 201 })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
