import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

// GET /api/customers/[id] - 获取单个客户
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const { data, error } = await supabase
      .from('customers')
      .select('*')
      .eq('id', params.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: '客户不存在' }, { status: 404 })
      }
      console.error('获取客户失败:', error)
      return NextResponse.json({ error: '获取客户失败' }, { status: 500 })
    }

    return NextResponse.json({ data })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}

// PUT /api/customers/[id] - 更新客户
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const body = await request.json()
    const {
      first_name,
      last_name,
      phone,
      email,
      date_of_birth,
      gender,
      address,
      notes,
      customer_type
    } = body

    // 验证必填字段
    if (!first_name || !last_name || !phone) {
      return NextResponse.json({ error: '姓名和电话是必填字段' }, { status: 400 })
    }

    // 检查电话号码是否被其他客户使用
    const { data: existingCustomer } = await supabase
      .from('customers')
      .select('id')
      .eq('phone', phone)
      .neq('id', params.id)
      .single()

    if (existingCustomer) {
      return NextResponse.json({ error: '该电话号码已被其他客户使用' }, { status: 400 })
    }

    // 更新客户
    const { data, error } = await supabase
      .from('customers')
      .update({
        first_name,
        last_name,
        phone,
        email,
        date_of_birth,
        gender,
        address,
        notes,
        customer_type,
        updated_at: new Date().toISOString()
      })
      .eq('id', params.id)
      .select()
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: '客户不存在' }, { status: 404 })
      }
      console.error('更新客户失败:', error)
      return NextResponse.json({ error: '更新客户失败' }, { status: 500 })
    }

    return NextResponse.json({ data })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}

// DELETE /api/customers/[id] - 删除客户
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createRouteHandlerClient({ cookies })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    // 检查客户是否有相关的预约或订单
    const { data: appointments } = await supabase
      .from('appointments')
      .select('id')
      .eq('customer_id', params.id)
      .limit(1)

    if (appointments && appointments.length > 0) {
      return NextResponse.json({ 
        error: '无法删除客户，该客户有相关的预约记录' 
      }, { status: 400 })
    }

    const { data: orders } = await supabase
      .from('orders')
      .select('id')
      .eq('customer_id', params.id)
      .limit(1)

    if (orders && orders.length > 0) {
      return NextResponse.json({ 
        error: '无法删除客户，该客户有相关的订单记录' 
      }, { status: 400 })
    }

    // 删除客户
    const { error } = await supabase
      .from('customers')
      .delete()
      .eq('id', params.id)

    if (error) {
      console.error('删除客户失败:', error)
      return NextResponse.json({ error: '删除客户失败' }, { status: 500 })
    }

    return NextResponse.json({ message: '客户删除成功' })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
