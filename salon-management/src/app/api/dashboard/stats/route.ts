import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

// GET /api/dashboard/stats - 获取仪表板统计数据
export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })

    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    // 调用数据库函数获取统计数据
    const { data, error } = await supabase.rpc('get_dashboard_stats')

    if (error) {
      console.error('获取统计数据失败:', error)
      return NextResponse.json({ error: '获取统计数据失败' }, { status: 500 })
    }

    return NextResponse.json({ data })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
