import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

// GET /api/staff/[id] - 获取单个员工
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', params.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: '员工不存在' }, { status: 404 })
      }
      console.error('获取员工失败:', error)
      return NextResponse.json({ error: '获取员工失败' }, { status: 500 })
    }

    return NextResponse.json({ data })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}

// PUT /api/staff/[id] - 更新员工
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const body = await request.json()
    const {
      full_name,
      phone,
      role,
      hire_date,
      salary,
      commission_rate,
      is_active
    } = body

    // 验证必填字段
    if (!full_name) {
      return NextResponse.json({ error: '姓名是必填字段' }, { status: 400 })
    }

    // 更新员工
    const { data, error } = await supabase
      .from('profiles')
      .update({
        full_name,
        phone,
        role,
        hire_date,
        salary,
        commission_rate,
        is_active,
        updated_at: new Date().toISOString()
      })
      .eq('id', params.id)
      .select()
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: '员工不存在' }, { status: 404 })
      }
      console.error('更新员工失败:', error)
      return NextResponse.json({ error: '更新员工失败' }, { status: 500 })
    }

    return NextResponse.json({ data })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}

// DELETE /api/staff/[id] - 删除员工
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    // 检查员工是否有相关的预约或排班
    const { data: appointments } = await supabase
      .from('appointments')
      .select('id')
      .eq('staff_id', params.id)
      .limit(1)

    if (appointments && appointments.length > 0) {
      return NextResponse.json({ 
        error: '无法删除员工，该员工有相关的预约记录' 
      }, { status: 400 })
    }

    const { data: schedules } = await supabase
      .from('schedules')
      .select('id')
      .eq('staff_id', params.id)
      .limit(1)

    if (schedules && schedules.length > 0) {
      return NextResponse.json({ 
        error: '无法删除员工，该员工有相关的排班记录' 
      }, { status: 400 })
    }

    // 软删除员工（设置为不活跃）
    const { error } = await supabase
      .from('profiles')
      .update({ is_active: false })
      .eq('id', params.id)

    if (error) {
      console.error('删除员工失败:', error)
      return NextResponse.json({ error: '删除员工失败' }, { status: 500 })
    }

    return NextResponse.json({ message: '员工已停用' })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
