import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

// GET /api/appointments - 获取预约列表
export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const date = searchParams.get('date') || ''
    const status = searchParams.get('status') || ''
    const staffId = searchParams.get('staff_id') || ''
    const customerId = searchParams.get('customer_id') || ''

    let query = supabase
      .from('appointments')
      .select(`
        *,
        customers (id, first_name, last_name, phone),
        profiles (id, full_name),
        services (id, name, price, duration)
      `, { count: 'exact' })
      .order('appointment_date', { ascending: true })

    // 日期过滤
    if (date) {
      query = query.eq('appointment_date', date)
    }

    // 状态过滤
    if (status) {
      query = query.eq('status', status)
    }

    // 员工过滤
    if (staffId) {
      query = query.eq('staff_id', staffId)
    }

    // 客户过滤
    if (customerId) {
      query = query.eq('customer_id', customerId)
    }

    // 分页
    const from = (page - 1) * limit
    const to = from + limit - 1
    query = query.range(from, to)

    const { data, error, count } = await query

    if (error) {
      console.error('获取预约列表失败:', error)
      return NextResponse.json({ error: '获取预约列表失败' }, { status: 500 })
    }

    return NextResponse.json({
      data,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}

// POST /api/appointments - 创建新预约
export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const body = await request.json()
    const {
      customer_id,
      staff_id,
      service_id,
      appointment_date,
      start_time,
      end_time,
      notes,
      duration_minutes,
      price
    } = body

    // 验证必填字段
    if (!customer_id || !staff_id || !service_id || !appointment_date || !start_time || !end_time) {
      return NextResponse.json({ error: '客户、员工、服务、日期和时间是必填字段' }, { status: 400 })
    }

    // 检查时间冲突
    const appointmentStart = new Date(`${appointment_date}T${start_time}`)
    const appointmentEnd = new Date(`${appointment_date}T${end_time}`)

    const { data: conflicts } = await supabase
      .from('appointments')
      .select('id')
      .eq('staff_id', staff_id)
      .eq('appointment_date', appointment_date)
      .in('status', ['scheduled', 'in_progress'])
      .or(`start_time.lte.${end_time},end_time.gte.${start_time}`)

    if (conflicts && conflicts.length > 0) {
      return NextResponse.json({ error: '该时间段已有预约，请选择其他时间' }, { status: 400 })
    }

    // 创建预约
    const { data, error } = await supabase
      .from('appointments')
      .insert({
        customer_id,
        staff_id,
        service_id,
        appointment_date,
        start_time,
        end_time,
        notes,
        duration_minutes,
        price,
        total_amount: price || 0,
        status: 'scheduled',
        created_by: session.user.id,
        appointment_datetime: appointmentStart.toISOString()
      })
      .select(`
        *,
        customers (id, first_name, last_name, phone),
        profiles (id, full_name),
        services (id, name, price, duration)
      `)
      .single()

    if (error) {
      console.error('创建预约失败:', error)
      return NextResponse.json({ error: '创建预约失败' }, { status: 500 })
    }

    return NextResponse.json({ data }, { status: 201 })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
