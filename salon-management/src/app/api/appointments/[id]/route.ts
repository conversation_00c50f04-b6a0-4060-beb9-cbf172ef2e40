import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

// GET /api/appointments/[id] - 获取单个预约
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const { data, error } = await supabase
      .from('appointments')
      .select(`
        *,
        customers (id, first_name, last_name, phone, email),
        profiles (id, full_name, phone),
        services (id, name, price, duration, category)
      `)
      .eq('id', params.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: '预约不存在' }, { status: 404 })
      }
      console.error('获取预约失败:', error)
      return NextResponse.json({ error: '获取预约失败' }, { status: 500 })
    }

    return NextResponse.json({ data })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}

// PUT /api/appointments/[id] - 更新预约
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const body = await request.json()
    const {
      customer_id,
      staff_id,
      service_id,
      appointment_date,
      start_time,
      end_time,
      status,
      notes,
      duration_minutes,
      price,
      actual_start_time,
      actual_end_time
    } = body

    // 如果更新时间，检查冲突
    if (appointment_date && start_time && end_time && staff_id) {
      const { data: conflicts } = await supabase
        .from('appointments')
        .select('id')
        .eq('staff_id', staff_id)
        .eq('appointment_date', appointment_date)
        .neq('id', params.id)
        .in('status', ['scheduled', 'in_progress'])
        .or(`start_time.lte.${end_time},end_time.gte.${start_time}`)

      if (conflicts && conflicts.length > 0) {
        return NextResponse.json({ error: '该时间段已有预约，请选择其他时间' }, { status: 400 })
      }
    }

    // 构建更新数据
    const updateData: any = {
      updated_at: new Date().toISOString()
    }

    if (customer_id) updateData.customer_id = customer_id
    if (staff_id) updateData.staff_id = staff_id
    if (service_id) updateData.service_id = service_id
    if (appointment_date) updateData.appointment_date = appointment_date
    if (start_time) updateData.start_time = start_time
    if (end_time) updateData.end_time = end_time
    if (status) updateData.status = status
    if (notes !== undefined) updateData.notes = notes
    if (duration_minutes) updateData.duration_minutes = duration_minutes
    if (price !== undefined) {
      updateData.price = price
      updateData.total_amount = price
    }
    if (actual_start_time) updateData.actual_start_time = actual_start_time
    if (actual_end_time) updateData.actual_end_time = actual_end_time

    // 如果有日期和时间更新，更新datetime字段
    if (appointment_date && start_time) {
      const appointmentStart = new Date(`${appointment_date}T${start_time}`)
      updateData.appointment_datetime = appointmentStart.toISOString()
    }

    // 更新预约
    const { data, error } = await supabase
      .from('appointments')
      .update(updateData)
      .eq('id', params.id)
      .select(`
        *,
        customers (id, first_name, last_name, phone, email),
        profiles (id, full_name, phone),
        services (id, name, price, duration, category)
      `)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: '预约不存在' }, { status: 404 })
      }
      console.error('更新预约失败:', error)
      return NextResponse.json({ error: '更新预约失败' }, { status: 500 })
    }

    return NextResponse.json({ data })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}

// DELETE /api/appointments/[id] - 取消预约
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    // 软删除：将状态设置为已取消
    const { data, error } = await supabase
      .from('appointments')
      .update({ 
        status: 'cancelled',
        updated_at: new Date().toISOString()
      })
      .eq('id', params.id)
      .select()
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: '预约不存在' }, { status: 404 })
      }
      console.error('取消预约失败:', error)
      return NextResponse.json({ error: '取消预约失败' }, { status: 500 })
    }

    return NextResponse.json({ message: '预约已取消', data })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
