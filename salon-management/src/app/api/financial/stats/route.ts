import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

// GET /api/financial/stats - 获取财务统计数据
export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || 'month' // day, week, month, year
    const startDate = searchParams.get('start_date')
    const endDate = searchParams.get('end_date')

    // 计算日期范围
    let dateFilter = ''
    const now = new Date()
    
    if (startDate && endDate) {
      dateFilter = `AND transaction_date >= '${startDate}' AND transaction_date <= '${endDate}'`
    } else {
      switch (period) {
        case 'day':
          const today = now.toISOString().split('T')[0]
          dateFilter = `AND transaction_date >= '${today}' AND transaction_date < '${today}'::date + interval '1 day'`
          break
        case 'week':
          const weekStart = new Date(now.setDate(now.getDate() - now.getDay())).toISOString().split('T')[0]
          dateFilter = `AND transaction_date >= '${weekStart}' AND transaction_date < '${weekStart}'::date + interval '7 days'`
          break
        case 'month':
          const monthStart = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0]
          dateFilter = `AND transaction_date >= '${monthStart}' AND transaction_date < '${monthStart}'::date + interval '1 month'`
          break
        case 'year':
          const yearStart = new Date(now.getFullYear(), 0, 1).toISOString().split('T')[0]
          dateFilter = `AND transaction_date >= '${yearStart}' AND transaction_date < '${yearStart}'::date + interval '1 year'`
          break
      }
    }

    // 获取收入统计
    const { data: incomeStats } = await supabase.rpc('get_financial_stats', {
      transaction_type_filter: 'income',
      date_filter: dateFilter
    }).single()

    // 获取支出统计
    const { data: expenseStats } = await supabase.rpc('get_financial_stats', {
      transaction_type_filter: 'expense',
      date_filter: dateFilter
    }).single()

    // 如果函数不存在，手动计算
    let totalIncome = 0
    let totalExpense = 0
    let incomeByCategory: any[] = []
    let expenseByCategory: any[] = []

    // 手动计算收入
    const { data: incomeData } = await supabase
      .from('financial_transactions')
      .select('amount, category')
      .eq('transaction_type', 'income')

    if (incomeData) {
      totalIncome = incomeData.reduce((sum, item) => sum + parseFloat(item.amount), 0)
      
      const incomeCategories = incomeData.reduce((acc: any, item) => {
        acc[item.category] = (acc[item.category] || 0) + parseFloat(item.amount)
        return acc
      }, {})
      
      incomeByCategory = Object.entries(incomeCategories).map(([category, amount]) => ({
        category,
        amount
      }))
    }

    // 手动计算支出
    const { data: expenseData } = await supabase
      .from('financial_transactions')
      .select('amount, category')
      .eq('transaction_type', 'expense')

    if (expenseData) {
      totalExpense = expenseData.reduce((sum, item) => sum + parseFloat(item.amount), 0)
      
      const expenseCategories = expenseData.reduce((acc: any, item) => {
        acc[item.category] = (acc[item.category] || 0) + parseFloat(item.amount)
        return acc
      }, {})
      
      expenseByCategory = Object.entries(expenseCategories).map(([category, amount]) => ({
        category,
        amount
      }))
    }

    // 获取订单统计
    const { data: orderStats } = await supabase
      .from('orders')
      .select('total, payment_status, order_date')
      .eq('payment_status', 'completed')

    let orderRevenue = 0
    let orderCount = 0
    if (orderStats) {
      orderRevenue = orderStats.reduce((sum, order) => sum + parseFloat(order.total), 0)
      orderCount = orderStats.length
    }

    // 获取会员卡统计
    const { data: membershipStats } = await supabase
      .from('membership_cards')
      .select('balance, points, is_active')

    let totalMembershipBalance = 0
    let activeMembershipCount = 0
    if (membershipStats) {
      totalMembershipBalance = membershipStats
        .filter(card => card.is_active)
        .reduce((sum, card) => sum + parseFloat(card.balance), 0)
      activeMembershipCount = membershipStats.filter(card => card.is_active).length
    }

    // 计算利润
    const profit = totalIncome - totalExpense

    // 获取趋势数据（最近7天）
    const { data: trendData } = await supabase
      .from('financial_transactions')
      .select('transaction_date, transaction_type, amount')
      .gte('transaction_date', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
      .order('transaction_date', { ascending: true })

    const dailyTrends = trendData?.reduce((acc: any, item) => {
      const date = item.transaction_date.split('T')[0]
      if (!acc[date]) {
        acc[date] = { date, income: 0, expense: 0 }
      }
      if (item.transaction_type === 'income') {
        acc[date].income += parseFloat(item.amount)
      } else if (item.transaction_type === 'expense') {
        acc[date].expense += parseFloat(item.amount)
      }
      return acc
    }, {})

    const trends = Object.values(dailyTrends || {})

    return NextResponse.json({
      data: {
        summary: {
          total_income: totalIncome,
          total_expense: totalExpense,
          profit,
          profit_margin: totalIncome > 0 ? (profit / totalIncome * 100).toFixed(2) : 0,
          order_revenue: orderRevenue,
          order_count: orderCount,
          membership_balance: totalMembershipBalance,
          active_memberships: activeMembershipCount
        },
        income_by_category: incomeByCategory,
        expense_by_category: expenseByCategory,
        trends,
        period
      }
    })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
