import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

// GET /api/financial/transactions - 获取财务交易列表
export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const type = searchParams.get('type') || ''
    const category = searchParams.get('category') || ''
    const startDate = searchParams.get('start_date') || ''
    const endDate = searchParams.get('end_date') || ''
    const staffId = searchParams.get('staff_id') || ''

    let query = supabase
      .from('financial_transactions')
      .select(`
        *,
        staff:profiles!staff_id (id, full_name),
        customer:customers!customer_id (id, first_name, last_name),
        created_by_user:profiles!created_by (id, full_name)
      `, { count: 'exact' })
      .order('transaction_date', { ascending: false })

    // 类型过滤
    if (type) {
      query = query.eq('transaction_type', type)
    }

    // 分类过滤
    if (category) {
      query = query.eq('category', category)
    }

    // 日期范围过滤
    if (startDate) {
      query = query.gte('transaction_date', startDate)
    }
    if (endDate) {
      query = query.lte('transaction_date', endDate)
    }

    // 员工过滤
    if (staffId) {
      query = query.eq('staff_id', staffId)
    }

    // 分页
    const from = (page - 1) * limit
    const to = from + limit - 1
    query = query.range(from, to)

    const { data, error, count } = await query

    if (error) {
      console.error('获取财务交易失败:', error)
      return NextResponse.json({ error: '获取财务交易失败' }, { status: 500 })
    }

    return NextResponse.json({
      data,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}

// POST /api/financial/transactions - 创建新财务交易
export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const body = await request.json()
    const {
      transaction_type,
      category,
      amount,
      description,
      reference_type,
      reference_id,
      staff_id,
      customer_id,
      payment_method,
      transaction_date
    } = body

    // 验证必填字段
    if (!transaction_type || !category || !amount) {
      return NextResponse.json({ error: '交易类型、分类和金额是必填字段' }, { status: 400 })
    }

    // 验证金额
    if (parseFloat(amount) <= 0) {
      return NextResponse.json({ error: '金额必须大于0' }, { status: 400 })
    }

    // 创建财务交易
    const { data, error } = await supabase
      .from('financial_transactions')
      .insert({
        transaction_type,
        category,
        amount: parseFloat(amount),
        description,
        reference_type,
        reference_id,
        staff_id,
        customer_id,
        payment_method,
        transaction_date: transaction_date || new Date().toISOString(),
        created_by: session.user.id
      })
      .select(`
        *,
        staff:profiles!staff_id (id, full_name),
        customer:customers!customer_id (id, first_name, last_name),
        created_by_user:profiles!created_by (id, full_name)
      `)
      .single()

    if (error) {
      console.error('创建财务交易失败:', error)
      return NextResponse.json({ error: '创建财务交易失败' }, { status: 500 })
    }

    return NextResponse.json({ data }, { status: 201 })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
