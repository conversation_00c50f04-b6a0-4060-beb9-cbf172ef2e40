import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

// GET /api/inventory/products - 获取产品列表
export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search') || ''
    const categoryId = searchParams.get('category_id') || ''
    const lowStock = searchParams.get('low_stock') === 'true'
    const isActive = searchParams.get('is_active')

    let query = supabase
      .from('products')
      .select(`
        *,
        product_categories (id, name)
      `, { count: 'exact' })
      .order('created_at', { ascending: false })

    // 搜索过滤
    if (search) {
      query = query.or(`name.ilike.%${search}%,sku.ilike.%${search}%,brand.ilike.%${search}%`)
    }

    // 分类过滤
    if (categoryId) {
      query = query.eq('category_id', categoryId)
    }

    // 低库存过滤
    if (lowStock) {
      query = query.lt('current_stock', 'min_stock_level')
    }

    // 状态过滤
    if (isActive !== null && isActive !== '') {
      query = query.eq('is_active', isActive === 'true')
    }

    // 分页
    const from = (page - 1) * limit
    const to = from + limit - 1
    query = query.range(from, to)

    const { data, error, count } = await query

    if (error) {
      console.error('获取产品列表失败:', error)
      return NextResponse.json({ error: '获取产品列表失败' }, { status: 500 })
    }

    return NextResponse.json({
      data,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}

// POST /api/inventory/products - 创建新产品
export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const body = await request.json()
    const {
      name,
      sku,
      barcode,
      category_id,
      brand,
      description,
      unit = '个',
      cost_price = 0,
      selling_price = 0,
      min_stock_level = 0,
      max_stock_level = 1000,
      current_stock = 0,
      image_url
    } = body

    // 验证必填字段
    if (!name) {
      return NextResponse.json({ error: '产品名称是必填字段' }, { status: 400 })
    }

    // 检查SKU是否已存在
    if (sku) {
      const { data: existingProduct } = await supabase
        .from('products')
        .select('id')
        .eq('sku', sku)
        .single()

      if (existingProduct) {
        return NextResponse.json({ error: '该SKU已存在' }, { status: 400 })
      }
    }

    // 创建产品
    const { data, error } = await supabase
      .from('products')
      .insert({
        name,
        sku,
        barcode,
        category_id,
        brand,
        description,
        unit,
        cost_price: parseFloat(cost_price),
        selling_price: parseFloat(selling_price),
        min_stock_level: parseInt(min_stock_level),
        max_stock_level: parseInt(max_stock_level),
        current_stock: parseInt(current_stock)
      })
      .select(`
        *,
        product_categories (id, name)
      `)
      .single()

    if (error) {
      console.error('创建产品失败:', error)
      return NextResponse.json({ error: '创建产品失败' }, { status: 500 })
    }

    // 如果有初始库存，创建库存流水记录
    if (parseInt(current_stock) > 0) {
      await supabase
        .from('inventory_movements')
        .insert({
          product_id: data.id,
          movement_type: 'in',
          quantity: parseInt(current_stock),
          unit_cost: parseFloat(cost_price),
          total_cost: parseInt(current_stock) * parseFloat(cost_price),
          reason: '初始库存',
          reference_type: 'initial',
          stock_before: 0,
          stock_after: parseInt(current_stock),
          created_by: session.user.id
        })
    }

    return NextResponse.json({ data }, { status: 201 })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
