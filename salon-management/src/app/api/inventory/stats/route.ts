import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

// GET /api/inventory/stats - 获取库存统计数据
export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    // 获取产品总数
    const { count: totalProducts } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true)

    // 获取低库存产品数量
    const { data: lowStockProducts } = await supabase
      .from('products')
      .select('id, name, current_stock, min_stock_level')
      .eq('is_active', true)
      .lt('current_stock', 'min_stock_level')

    // 获取缺货产品数量
    const { count: outOfStockCount } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true)
      .eq('current_stock', 0)

    // 计算库存总价值
    const { data: inventoryValue } = await supabase
      .from('products')
      .select('current_stock, cost_price')
      .eq('is_active', true)

    let totalInventoryValue = 0
    if (inventoryValue) {
      totalInventoryValue = inventoryValue.reduce((sum, product) => {
        return sum + (product.current_stock * parseFloat(product.cost_price))
      }, 0)
    }

    // 获取分类统计
    const { data: categoryStats } = await supabase
      .from('product_categories')
      .select(`
        id,
        name,
        products!inner (id, current_stock, cost_price)
      `)
      .eq('is_active', true)
      .eq('products.is_active', true)

    const categoryData = categoryStats?.map(category => {
      const productCount = category.products.length
      const totalStock = category.products.reduce((sum: number, product: any) => sum + product.current_stock, 0)
      const totalValue = category.products.reduce((sum: number, product: any) => 
        sum + (product.current_stock * parseFloat(product.cost_price)), 0)
      
      return {
        category: category.name,
        product_count: productCount,
        total_stock: totalStock,
        total_value: totalValue
      }
    }) || []

    // 获取最近库存变动
    const { data: recentMovements } = await supabase
      .from('inventory_movements')
      .select(`
        *,
        products (name, sku)
      `)
      .order('created_at', { ascending: false })
      .limit(10)

    // 获取待处理采购订单
    const { count: pendingPurchaseOrders } = await supabase
      .from('purchase_orders')
      .select('*', { count: 'exact', head: true })
      .in('status', ['pending', 'confirmed', 'shipped'])

    // 计算本月库存变动趋势
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    const { data: movementTrends } = await supabase
      .from('inventory_movements')
      .select('created_at, movement_type, quantity, total_cost')
      .gte('created_at', thirtyDaysAgo.toISOString())
      .order('created_at', { ascending: true })

    // 按日期分组统计
    const dailyTrends = movementTrends?.reduce((acc: any, movement) => {
      const date = movement.created_at.split('T')[0]
      if (!acc[date]) {
        acc[date] = { date, in: 0, out: 0, value_in: 0, value_out: 0 }
      }
      
      if (movement.movement_type === 'in') {
        acc[date].in += movement.quantity
        acc[date].value_in += parseFloat(movement.total_cost || '0')
      } else if (movement.movement_type === 'out') {
        acc[date].out += movement.quantity
        acc[date].value_out += parseFloat(movement.total_cost || '0')
      }
      
      return acc
    }, {})

    const trends = Object.values(dailyTrends || {})

    // 获取供应商统计
    const { count: totalSuppliers } = await supabase
      .from('suppliers')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true)

    return NextResponse.json({
      data: {
        summary: {
          total_products: totalProducts || 0,
          low_stock_count: lowStockProducts?.length || 0,
          out_of_stock_count: outOfStockCount || 0,
          total_inventory_value: totalInventoryValue,
          pending_purchase_orders: pendingPurchaseOrders || 0,
          total_suppliers: totalSuppliers || 0
        },
        low_stock_products: lowStockProducts || [],
        category_stats: categoryData,
        recent_movements: recentMovements || [],
        movement_trends: trends
      }
    })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
