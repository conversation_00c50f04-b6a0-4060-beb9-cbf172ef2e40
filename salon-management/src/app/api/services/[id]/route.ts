import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

// GET /api/services/[id] - 获取单个服务项目
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const { data, error } = await supabase
      .from('services')
      .select(`
        *,
        service_categories (id, name, description)
      `)
      .eq('id', params.id)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: '服务项目不存在' }, { status: 404 })
      }
      console.error('获取服务失败:', error)
      return NextResponse.json({ error: '获取服务失败' }, { status: 500 })
    }

    return NextResponse.json({ data })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}

// PUT /api/services/[id] - 更新服务项目
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const body = await request.json()
    const {
      name,
      description,
      category_id,
      category,
      duration,
      price,
      vip_price,
      cost,
      required_skill_level,
      image_url,
      tags,
      preparation_time,
      cleanup_time,
      is_active
    } = body

    // 验证必填字段
    if (!name || !duration || !price) {
      return NextResponse.json({ error: '服务名称、时长和价格是必填字段' }, { status: 400 })
    }

    // 检查服务名称是否被其他服务使用
    const { data: existingService } = await supabase
      .from('services')
      .select('id')
      .eq('name', name)
      .neq('id', params.id)
      .single()

    if (existingService) {
      return NextResponse.json({ error: '该服务名称已被其他服务使用' }, { status: 400 })
    }

    // 更新服务项目
    const { data, error } = await supabase
      .from('services')
      .update({
        name,
        description,
        category_id,
        category,
        duration,
        price,
        vip_price,
        cost,
        required_skill_level,
        image_url,
        tags,
        preparation_time,
        cleanup_time,
        is_active,
        updated_at: new Date().toISOString()
      })
      .eq('id', params.id)
      .select(`
        *,
        service_categories (id, name, description)
      `)
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: '服务项目不存在' }, { status: 404 })
      }
      console.error('更新服务失败:', error)
      return NextResponse.json({ error: '更新服务失败' }, { status: 500 })
    }

    return NextResponse.json({ data })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}

// DELETE /api/services/[id] - 删除服务项目
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    // 检查服务是否有相关的预约或订单
    const { data: appointments } = await supabase
      .from('appointments')
      .select('id')
      .eq('service_id', params.id)
      .limit(1)

    if (appointments && appointments.length > 0) {
      return NextResponse.json({ 
        error: '无法删除服务，该服务有相关的预约记录' 
      }, { status: 400 })
    }

    const { data: orderItems } = await supabase
      .from('order_items')
      .select('id')
      .eq('service_id', params.id)
      .limit(1)

    if (orderItems && orderItems.length > 0) {
      return NextResponse.json({ 
        error: '无法删除服务，该服务有相关的订单记录' 
      }, { status: 400 })
    }

    // 软删除服务（设置为不活跃）
    const { error } = await supabase
      .from('services')
      .update({ is_active: false })
      .eq('id', params.id)

    if (error) {
      console.error('删除服务失败:', error)
      return NextResponse.json({ error: '删除服务失败' }, { status: 500 })
    }

    return NextResponse.json({ message: '服务已停用' })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
