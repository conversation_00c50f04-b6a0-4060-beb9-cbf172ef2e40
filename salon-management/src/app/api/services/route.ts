import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

// GET /api/services - 获取服务项目列表
export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search') || ''
    const category = searchParams.get('category') || ''
    const categoryId = searchParams.get('category_id') || ''
    const isActive = searchParams.get('is_active')

    let query = supabase
      .from('services')
      .select(`
        *,
        service_categories (id, name, description)
      `, { count: 'exact' })
      .order('created_at', { ascending: false })

    // 搜索过滤
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`)
    }

    // 分类过滤（旧字段）
    if (category) {
      query = query.eq('category', category)
    }

    // 分类过滤（新字段）
    if (categoryId) {
      query = query.eq('category_id', categoryId)
    }

    // 状态过滤
    if (isActive !== null && isActive !== '') {
      query = query.eq('is_active', isActive === 'true')
    }

    // 分页
    const from = (page - 1) * limit
    const to = from + limit - 1
    query = query.range(from, to)

    const { data, error, count } = await query

    if (error) {
      console.error('获取服务列表失败:', error)
      return NextResponse.json({ error: '获取服务列表失败' }, { status: 500 })
    }

    return NextResponse.json({
      data,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}

// POST /api/services - 创建新服务项目
export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const body = await request.json()
    const {
      name,
      description,
      category_id,
      category, // 兼容旧字段
      duration,
      price,
      vip_price,
      cost = 0.00,
      required_skill_level = 1,
      image_url,
      tags = [],
      preparation_time = 0,
      cleanup_time = 0
    } = body

    // 验证必填字段
    if (!name || !duration || !price) {
      return NextResponse.json({ error: '服务名称、时长和价格是必填字段' }, { status: 400 })
    }

    // 检查服务名称是否已存在
    const { data: existingService } = await supabase
      .from('services')
      .select('id')
      .eq('name', name)
      .single()

    if (existingService) {
      return NextResponse.json({ error: '该服务名称已存在' }, { status: 400 })
    }

    // 创建服务项目
    const { data, error } = await supabase
      .from('services')
      .insert({
        name,
        description,
        category_id,
        category: category || 'other', // 兼容旧字段
        duration,
        price,
        vip_price,
        cost,
        required_skill_level,
        image_url,
        tags,
        preparation_time,
        cleanup_time
      })
      .select(`
        *,
        service_categories (id, name, description)
      `)
      .single()

    if (error) {
      console.error('创建服务失败:', error)
      return NextResponse.json({ error: '创建服务失败' }, { status: 500 })
    }

    return NextResponse.json({ data }, { status: 201 })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
