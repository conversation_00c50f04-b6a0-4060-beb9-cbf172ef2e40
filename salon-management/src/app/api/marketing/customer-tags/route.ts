import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

// GET /api/marketing/customer-tags - 获取客户标签列表
export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const withCount = searchParams.get('with_count') === 'true'
    const isActive = searchParams.get('is_active')

    let query = supabase
      .from('customer_tags')
      .select('*')
      .order('name')

    // 状态过滤
    if (isActive !== null && isActive !== '') {
      query = query.eq('is_active', isActive === 'true')
    }

    const { data: tags, error } = await query

    if (error) {
      console.error('获取客户标签失败:', error)
      return NextResponse.json({ error: '获取客户标签失败' }, { status: 500 })
    }

    // 如果需要统计每个标签的客户数量
    if (withCount && tags) {
      const tagsWithCount = await Promise.all(
        tags.map(async (tag) => {
          const { count } = await supabase
            .from('customer_tag_relations')
            .select('*', { count: 'exact', head: true })
            .eq('tag_id', tag.id)

          return {
            ...tag,
            customer_count: count || 0
          }
        })
      )

      return NextResponse.json({ data: tagsWithCount })
    }

    return NextResponse.json({ data: tags })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}

// POST /api/marketing/customer-tags - 创建新客户标签
export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const body = await request.json()
    const {
      name,
      description,
      color = '#3B82F6'
    } = body

    // 验证必填字段
    if (!name) {
      return NextResponse.json({ error: '标签名称是必填字段' }, { status: 400 })
    }

    // 检查标签名称是否已存在
    const { data: existingTag } = await supabase
      .from('customer_tags')
      .select('id')
      .eq('name', name)
      .single()

    if (existingTag) {
      return NextResponse.json({ error: '该标签名称已存在' }, { status: 400 })
    }

    // 创建客户标签
    const { data, error } = await supabase
      .from('customer_tags')
      .insert({
        name,
        description,
        color
      })
      .select()
      .single()

    if (error) {
      console.error('创建客户标签失败:', error)
      return NextResponse.json({ error: '创建客户标签失败' }, { status: 500 })
    }

    return NextResponse.json({ data }, { status: 201 })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
