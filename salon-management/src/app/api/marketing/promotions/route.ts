import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

// GET /api/marketing/promotions - 获取促销活动列表
export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '20')
    const search = searchParams.get('search') || ''
    const type = searchParams.get('type') || ''
    const status = searchParams.get('status') || ''
    const isActive = searchParams.get('is_active')

    let query = supabase
      .from('promotions')
      .select(`
        *,
        created_by_user:profiles!created_by (full_name)
      `, { count: 'exact' })
      .order('created_at', { ascending: false })

    // 搜索过滤
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`)
    }

    // 类型过滤
    if (type) {
      query = query.eq('promotion_type', type)
    }

    // 状态过滤（基于时间）
    const now = new Date().toISOString()
    if (status === 'active') {
      query = query.lte('start_date', now).gte('end_date', now).eq('is_active', true)
    } else if (status === 'upcoming') {
      query = query.gt('start_date', now).eq('is_active', true)
    } else if (status === 'expired') {
      query = query.lt('end_date', now)
    }

    // 活跃状态过滤
    if (isActive !== null && isActive !== '') {
      query = query.eq('is_active', isActive === 'true')
    }

    // 分页
    const from = (page - 1) * limit
    const to = from + limit - 1
    query = query.range(from, to)

    const { data, error, count } = await query

    if (error) {
      console.error('获取促销活动失败:', error)
      return NextResponse.json({ error: '获取促销活动失败' }, { status: 500 })
    }

    return NextResponse.json({
      data,
      pagination: {
        page,
        limit,
        total: count || 0,
        totalPages: Math.ceil((count || 0) / limit)
      }
    })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}

// POST /api/marketing/promotions - 创建新促销活动
export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const body = await request.json()
    const {
      name,
      description,
      promotion_type,
      discount_type,
      discount_value,
      min_purchase_amount = 0,
      max_discount_amount,
      applicable_services = [],
      applicable_products = [],
      customer_groups = [],
      start_date,
      end_date,
      usage_limit,
      customer_usage_limit = 1
    } = body

    // 验证必填字段
    if (!name || !promotion_type || !start_date || !end_date) {
      return NextResponse.json({ error: '活动名称、类型、开始时间和结束时间是必填字段' }, { status: 400 })
    }

    // 验证时间
    if (new Date(start_date) >= new Date(end_date)) {
      return NextResponse.json({ error: '结束时间必须晚于开始时间' }, { status: 400 })
    }

    // 验证折扣信息
    if (promotion_type === 'discount' && (!discount_type || !discount_value)) {
      return NextResponse.json({ error: '折扣活动必须设置折扣类型和折扣值' }, { status: 400 })
    }

    // 创建促销活动
    const { data, error } = await supabase
      .from('promotions')
      .insert({
        name,
        description,
        promotion_type,
        discount_type,
        discount_value: discount_value ? parseFloat(discount_value) : null,
        min_purchase_amount: parseFloat(min_purchase_amount),
        max_discount_amount: max_discount_amount ? parseFloat(max_discount_amount) : null,
        applicable_services,
        applicable_products,
        customer_groups,
        start_date,
        end_date,
        usage_limit: usage_limit ? parseInt(usage_limit) : null,
        customer_usage_limit: parseInt(customer_usage_limit),
        created_by: session.user.id
      })
      .select(`
        *,
        created_by_user:profiles!created_by (full_name)
      `)
      .single()

    if (error) {
      console.error('创建促销活动失败:', error)
      return NextResponse.json({ error: '创建促销活动失败' }, { status: 500 })
    }

    return NextResponse.json({ data }, { status: 201 })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
