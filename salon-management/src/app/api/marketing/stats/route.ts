import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

// GET /api/marketing/stats - 获取营销统计数据
export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies()
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore })
    
    // 验证用户是否已登录
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      return NextResponse.json({ error: '未授权访问' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const period = searchParams.get('period') || 'month' // day, week, month, year

    // 计算日期范围
    const now = new Date()
    let startDate = ''
    
    switch (period) {
      case 'day':
        startDate = now.toISOString().split('T')[0]
        break
      case 'week':
        const weekStart = new Date(now.setDate(now.getDate() - now.getDay()))
        startDate = weekStart.toISOString().split('T')[0]
        break
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0]
        break
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1).toISOString().split('T')[0]
        break
    }

    // 获取促销活动统计
    const { count: totalPromotions } = await supabase
      .from('promotions')
      .select('*', { count: 'exact', head: true })

    const { count: activePromotions } = await supabase
      .from('promotions')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true)
      .lte('start_date', new Date().toISOString())
      .gte('end_date', new Date().toISOString())

    // 获取促销使用统计
    const { data: promotionUsages } = await supabase
      .from('promotion_usages')
      .select('discount_amount')
      .gte('used_at', startDate)

    const totalPromotionUsage = promotionUsages?.length || 0
    const totalDiscountAmount = promotionUsages?.reduce((sum, usage) => 
      sum + parseFloat(usage.discount_amount), 0) || 0

    // 获取客户标签统计
    const { count: totalTags } = await supabase
      .from('customer_tags')
      .select('*', { count: 'exact', head: true })
      .eq('is_active', true)

    // 获取标签分布统计
    const { data: tagDistribution } = await supabase
      .from('customer_tags')
      .select(`
        id,
        name,
        color,
        customer_tag_relations!inner (customer_id)
      `)
      .eq('is_active', true)

    const tagStats = tagDistribution?.map(tag => ({
      tag_name: tag.name,
      tag_color: tag.color,
      customer_count: tag.customer_tag_relations?.length || 0
    })) || []

    // 获取客户回访统计
    const { count: totalFollowUps } = await supabase
      .from('customer_follow_ups')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', startDate)

    const { count: completedFollowUps } = await supabase
      .from('customer_follow_ups')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'completed')
      .gte('created_at', startDate)

    // 获取营销活动统计
    const { count: totalCampaigns } = await supabase
      .from('marketing_campaigns')
      .select('*', { count: 'exact', head: true })

    const { count: activeCampaigns } = await supabase
      .from('marketing_campaigns')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'active')

    // 获取推荐奖励统计
    const { count: totalReferrals } = await supabase
      .from('referral_rewards')
      .select('*', { count: 'exact', head: true })
      .gte('referral_date', startDate)

    const { data: referralRewards } = await supabase
      .from('referral_rewards')
      .select('reward_value')
      .eq('status', 'paid')
      .gte('referral_date', startDate)

    const totalReferralRewards = referralRewards?.reduce((sum, reward) => 
      sum + parseFloat(reward.reward_value), 0) || 0

    // 获取客户分类统计
    const { data: customerStats } = await supabase
      .from('customers')
      .select('customer_type, created_at')

    const customerTypeStats = customerStats?.reduce((acc: any, customer) => {
      acc[customer.customer_type] = (acc[customer.customer_type] || 0) + 1
      return acc
    }, {}) || {}

    const customerTypeDistribution = Object.entries(customerTypeStats).map(([type, count]) => ({
      customer_type: type,
      count
    }))

    // 获取最近的营销活动
    const { data: recentPromotions } = await supabase
      .from('promotions')
      .select(`
        *,
        promotion_usages (id, discount_amount)
      `)
      .order('created_at', { ascending: false })
      .limit(5)

    // 计算促销活动效果
    const promotionEffectiveness = recentPromotions?.map(promotion => {
      const usageCount = promotion.promotion_usages?.length || 0
      const totalDiscount = promotion.promotion_usages?.reduce((sum: number, usage: any) => 
        sum + parseFloat(usage.discount_amount), 0) || 0
      
      return {
        name: promotion.name,
        usage_count: usageCount,
        total_discount: totalDiscount,
        effectiveness: promotion.usage_limit ? (usageCount / promotion.usage_limit * 100).toFixed(1) : 'N/A'
      }
    }) || []

    return NextResponse.json({
      data: {
        summary: {
          total_promotions: totalPromotions || 0,
          active_promotions: activePromotions || 0,
          total_promotion_usage: totalPromotionUsage,
          total_discount_amount: totalDiscountAmount,
          total_tags: totalTags || 0,
          total_follow_ups: totalFollowUps || 0,
          completed_follow_ups: completedFollowUps || 0,
          follow_up_completion_rate: totalFollowUps > 0 ? ((completedFollowUps || 0) / totalFollowUps * 100).toFixed(1) : 0,
          total_campaigns: totalCampaigns || 0,
          active_campaigns: activeCampaigns || 0,
          total_referrals: totalReferrals || 0,
          total_referral_rewards: totalReferralRewards
        },
        tag_distribution: tagStats,
        customer_type_distribution: customerTypeDistribution,
        promotion_effectiveness: promotionEffectiveness,
        period
      }
    })
  } catch (error) {
    console.error('API错误:', error)
    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })
  }
}
