'use client'

import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { useEffect } from 'react'
import AddCustomerForm from '@/components/customers/AddCustomerForm'
import { Button } from '@/components/ui/button'

export default function NewCustomerPage() {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  const handleSuccess = (customer: any) => {
    router.push('/dashboard/customers')
  }

  const handleCancel = () => {
    router.push('/dashboard/customers')
  }

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
          <p className="mt-4">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">新增客户</h1>
              <p className="text-gray-600">添加新的客户信息</p>
            </div>
            <Button variant="outline" onClick={handleCancel}>
              返回客户列表
            </Button>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <AddCustomerForm onSuccess={handleSuccess} onCancel={handleCancel} />
        </div>
      </main>
    </div>
  )
}
