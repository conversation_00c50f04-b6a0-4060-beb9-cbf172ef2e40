'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { createSupabaseClient } from '@/lib/supabase'
import { Badge } from '@/components/ui/badge'

export default function CustomersPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [customers, setCustomers] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  useEffect(() => {
    if (user) {
      fetchCustomers()
    }
  }, [user])

  const fetchCustomers = async () => {
    try {
      setIsLoading(true)
      const { data, error } = await supabase
        .from('customers')
        .select('*')
        .order('created_at', { ascending: false })

      if (error) {
        console.error('获取客户数据失败:', error)
      } else {
        setCustomers(data || [])
      }
    } catch (error) {
      console.error('获取客户数据失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getCustomerTypeLabel = (type: string) => {
    const types = {
      'vip': 'VIP客户',
      'regular': '普通客户',
      'potential': '潜在客户',
      'lost': '流失客户'
    }
    return types[type as keyof typeof types] || type
  }

  const getCustomerTypeBadgeVariant = (type: string) => {
    switch (type) {
      case 'vip':
        return 'default'
      case 'regular':
        return 'secondary'
      case 'potential':
        return 'outline'
      case 'lost':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
          <p className="mt-4">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">客户管理</h1>
              <p className="text-gray-600">管理您的客户信息</p>
            </div>
            <div className="flex items-center space-x-4">
              <Button onClick={() => router.push('/dashboard')}>
                返回仪表板
              </Button>
              <Button>
                新增客户
              </Button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {isLoading ? (
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
              <p className="mt-4">加载客户数据中...</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {customers.length > 0 ? (
                customers.map((customer) => (
                  <Card key={customer.id} className="hover:shadow-lg transition-shadow">
                    <CardHeader>
                      <div className="flex justify-between items-start">
                        <div>
                          <CardTitle className="text-lg">
                            {customer.first_name} {customer.last_name}
                          </CardTitle>
                          <CardDescription>
                            {customer.phone}
                          </CardDescription>
                        </div>
                        <Badge variant={getCustomerTypeBadgeVariant(customer.customer_type)}>
                          {getCustomerTypeLabel(customer.customer_type)}
                        </Badge>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {customer.email && (
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">邮箱：</span>
                            {customer.email}
                          </p>
                        )}
                        {customer.date_of_birth && (
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">生日：</span>
                            {new Date(customer.date_of_birth).toLocaleDateString('zh-CN')}
                          </p>
                        )}
                        {customer.address && (
                          <p className="text-sm text-gray-600">
                            <span className="font-medium">地址：</span>
                            {customer.address}
                          </p>
                        )}
                        <div className="flex justify-between items-center pt-2">
                          <div className="text-sm text-gray-600">
                            <span className="font-medium">消费：</span>
                            ¥{customer.total_spent || 0}
                          </div>
                          <div className="text-sm text-gray-600">
                            <span className="font-medium">到店：</span>
                            {customer.visit_count || 0}次
                          </div>
                        </div>
                        {customer.notes && (
                          <p className="text-sm text-gray-600 pt-2 border-t">
                            <span className="font-medium">备注：</span>
                            {customer.notes}
                          </p>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                <div className="col-span-full text-center py-12">
                  <p className="text-gray-500 text-lg">暂无客户数据</p>
                  <p className="text-gray-400 text-sm mt-2">点击"新增客户"按钮添加第一个客户</p>
                </div>
              )}
            </div>
          )}
        </div>
      </main>
    </div>
  )
}
