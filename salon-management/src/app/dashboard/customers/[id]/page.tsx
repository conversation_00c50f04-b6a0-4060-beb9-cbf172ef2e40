'use client'

import { useEffect, useState } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { createSupabaseClient } from '@/lib/supabase'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { Pencil, Phone, Mail, MapPin, Calendar, User } from 'lucide-react'

export default function CustomerDetailPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const params = useParams()
  const [customer, setCustomer] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [appointments, setAppointments] = useState<any[]>([])
  const [orders, setOrders] = useState<any[]>([])
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  useEffect(() => {
    if (user && params.id) {
      fetchCustomerData()
    }
  }, [user, params.id])

  const fetchCustomerData = async () => {
    try {
      setIsLoading(true)
      
      // 获取客户基本信息
      const { data: customerData, error: customerError } = await supabase
        .from('customers')
        .select('*')
        .eq('id', params.id)
        .single()

      if (customerError) {
        console.error('获取客户信息失败:', customerError)
        router.push('/dashboard/customers')
        return
      }

      setCustomer(customerData)

      // 获取客户预约记录
      const { data: appointmentsData } = await supabase
        .from('appointments')
        .select(`
          *,
          services (name, price),
          profiles (full_name)
        `)
        .eq('customer_id', params.id)
        .order('appointment_date', { ascending: false })
        .limit(10)

      setAppointments(appointmentsData || [])

      // 获取客户订单记录
      const { data: ordersData } = await supabase
        .from('orders')
        .select('*')
        .eq('customer_id', params.id)
        .order('order_date', { ascending: false })
        .limit(10)

      setOrders(ordersData || [])

    } catch (error) {
      console.error('获取客户数据失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getCustomerTypeLabel = (type: string) => {
    const types = {
      'vip': 'VIP客户',
      'regular': '普通客户',
      'potential': '潜在客户',
      'lost': '流失客户'
    }
    return types[type as keyof typeof types] || type
  }

  const getCustomerTypeBadgeVariant = (type: string) => {
    switch (type) {
      case 'vip':
        return 'default'
      case 'regular':
        return 'secondary'
      case 'potential':
        return 'outline'
      case 'lost':
        return 'destructive'
      default:
        return 'secondary'
    }
  }

  const getAppointmentStatusLabel = (status: string) => {
    const statuses = {
      'scheduled': '已预约',
      'in_progress': '进行中',
      'completed': '已完成',
      'cancelled': '已取消',
      'no_show': '爽约'
    }
    return statuses[status as keyof typeof statuses] || status
  }

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
          <p className="mt-4">加载中...</p>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-4">加载客户信息中...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!customer) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="text-center py-12">
            <p className="text-gray-500">客户不存在</p>
            <Button 
              className="mt-4" 
              onClick={() => router.push('/dashboard/customers')}
            >
              返回客户列表
            </Button>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {customer.first_name} {customer.last_name}
            </h1>
            <p className="text-gray-600">客户详情</p>
          </div>
          <div className="flex space-x-2">
            <Button 
              variant="outline" 
              onClick={() => router.push('/dashboard/customers')}
            >
              返回列表
            </Button>
            <Button onClick={() => router.push(`/dashboard/customers/${customer.id}/edit`)}>
              <Pencil className="mr-2 h-4 w-4" />
              编辑
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 客户基本信息 */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  客户信息
                  <Badge variant={getCustomerTypeBadgeVariant(customer.customer_type)}>
                    {getCustomerTypeLabel(customer.customer_type)}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Phone className="h-4 w-4 text-gray-500" />
                  <span>{customer.phone}</span>
                </div>
                
                {customer.email && (
                  <div className="flex items-center space-x-3">
                    <Mail className="h-4 w-4 text-gray-500" />
                    <span>{customer.email}</span>
                  </div>
                )}
                
                {customer.date_of_birth && (
                  <div className="flex items-center space-x-3">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span>{new Date(customer.date_of_birth).toLocaleDateString('zh-CN')}</span>
                  </div>
                )}
                
                {customer.address && (
                  <div className="flex items-center space-x-3">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    <span>{customer.address}</span>
                  </div>
                )}
                
                <div className="pt-4 border-t">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">总消费</p>
                      <p className="font-medium">¥{customer.total_spent || 0}</p>
                    </div>
                    <div>
                      <p className="text-gray-500">到店次数</p>
                      <p className="font-medium">{customer.visit_count || 0}次</p>
                    </div>
                  </div>
                </div>
                
                {customer.notes && (
                  <div className="pt-4 border-t">
                    <p className="text-gray-500 text-sm">备注</p>
                    <p className="mt-1">{customer.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* 预约记录和订单记录 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 最近预约 */}
            <Card>
              <CardHeader>
                <CardTitle>最近预约</CardTitle>
                <CardDescription>最近10次预约记录</CardDescription>
              </CardHeader>
              <CardContent>
                {appointments.length > 0 ? (
                  <div className="space-y-4">
                    {appointments.map((appointment) => (
                      <div key={appointment.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <p className="font-medium">{appointment.services?.name || '未知服务'}</p>
                          <p className="text-sm text-gray-500">
                            {new Date(appointment.appointment_date).toLocaleString('zh-CN')}
                          </p>
                          <p className="text-sm text-gray-500">
                            服务师：{appointment.profiles?.full_name || '未指定'}
                          </p>
                        </div>
                        <div className="text-right">
                          <Badge variant="outline">
                            {getAppointmentStatusLabel(appointment.status)}
                          </Badge>
                          {appointment.price && (
                            <p className="text-sm font-medium mt-1">¥{appointment.price}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-8">暂无预约记录</p>
                )}
              </CardContent>
            </Card>

            {/* 消费记录 */}
            <Card>
              <CardHeader>
                <CardTitle>消费记录</CardTitle>
                <CardDescription>最近10次消费记录</CardDescription>
              </CardHeader>
              <CardContent>
                {orders.length > 0 ? (
                  <div className="space-y-4">
                    {orders.map((order) => (
                      <div key={order.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <p className="font-medium">订单 #{order.id.slice(0, 8)}</p>
                          <p className="text-sm text-gray-500">
                            {new Date(order.order_date).toLocaleString('zh-CN')}
                          </p>
                          <p className="text-sm text-gray-500">
                            支付方式：{order.payment_method || '未知'}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">¥{order.total}</p>
                          <Badge variant={order.payment_status === 'completed' ? 'default' : 'outline'}>
                            {order.payment_status === 'completed' ? '已支付' : '待支付'}
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-8">暂无消费记录</p>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
