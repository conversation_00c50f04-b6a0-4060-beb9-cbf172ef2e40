'use client'

import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { useEffect } from 'react'
import AddServiceForm from '@/components/services/AddServiceForm'
import { Button } from '@/components/ui/button'
import DashboardLayout from '@/components/layout/DashboardLayout'

export default function NewServicePage() {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  const handleSuccess = (service: any) => {
    router.push('/dashboard/services')
  }

  const handleCancel = () => {
    router.push('/dashboard/services')
  }

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
          <p className="mt-4">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">新增服务项目</h1>
            <p className="text-gray-600">添加新的服务项目</p>
          </div>
          <Button variant="outline" onClick={handleCancel}>
            返回服务列表
          </Button>
        </div>

        <AddServiceForm onSuccess={handleSuccess} onCancel={handleCancel} />
      </div>
    </DashboardLayout>
  )
}
