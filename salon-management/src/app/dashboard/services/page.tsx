'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { createSupabaseClient } from '@/lib/supabase'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { Scissors, Clock, DollarSign, Star, Tag, Image } from 'lucide-react'

export default function ServicesPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [services, setServices] = useState<any[]>([])
  const [categories, setCategories] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterCategory, setFilterCategory] = useState('')
  const [filterStatus, setFilterStatus] = useState('')
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  useEffect(() => {
    if (user) {
      fetchServices()
      fetchCategories()
    }
  }, [user, searchTerm, filterCategory, filterStatus])

  const fetchServices = async () => {
    try {
      setIsLoading(true)
      let query = supabase
        .from('services')
        .select(`
          *,
          service_categories (id, name, description)
        `)
        .order('created_at', { ascending: false })

      // 搜索过滤
      if (searchTerm) {
        query = query.or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`)
      }

      // 分类过滤
      if (filterCategory) {
        query = query.eq('category_id', filterCategory)
      }

      // 状态过滤
      if (filterStatus) {
        query = query.eq('is_active', filterStatus === 'active')
      }

      const { data, error } = await query

      if (error) {
        console.error('获取服务数据失败:', error)
      } else {
        setServices(data || [])
      }
    } catch (error) {
      console.error('获取服务数据失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const { data, error } = await supabase
        .from('service_categories')
        .select('*')
        .eq('is_active', true)
        .order('display_order', { ascending: true })

      if (!error) {
        setCategories(data || [])
      }
    } catch (error) {
      console.error('获取分类数据失败:', error)
    }
  }

  const handleSearch = (value: string) => {
    setSearchTerm(value)
  }

  const handleCategoryFilter = (value: string) => {
    setFilterCategory(value)
  }

  const handleStatusFilter = (value: string) => {
    setFilterStatus(value)
  }

  const getCategoryName = (service: any) => {
    return service.service_categories?.name || service.category || '未分类'
  }

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}分钟`
    }
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
  }

  const getSkillLevelText = (level: number) => {
    const levels = {
      1: '初级',
      2: '中级',
      3: '高级',
      4: '专家',
      5: '大师'
    }
    return levels[level as keyof typeof levels] || '未设置'
  }

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
          <p className="mt-4">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">服务项目管理</h1>
            <p className="text-gray-600">管理发廊的服务项目和价格</p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={() => router.push('/dashboard/services/categories')}>
              管理分类
            </Button>
            <Button onClick={() => router.push('/dashboard/services/new')}>
              新增服务
            </Button>
          </div>
        </div>

        {/* 搜索和过滤 */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <Input
              placeholder="搜索服务名称或描述..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full"
            />
          </div>
          <div className="w-full sm:w-48">
            <Select value={filterCategory} onValueChange={handleCategoryFilter}>
              <SelectTrigger>
                <SelectValue placeholder="服务分类" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部分类</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="w-full sm:w-32">
            <Select value={filterStatus} onValueChange={handleStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部状态</SelectItem>
                <SelectItem value="active">启用</SelectItem>
                <SelectItem value="inactive">停用</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {isLoading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-4">加载服务数据中...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {services.length > 0 ? (
              services.map((service) => (
                <Card key={service.id} className="hover:shadow-lg transition-shadow cursor-pointer"
                      onClick={() => router.push(`/dashboard/services/${service.id}`)}>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                          {service.image_url ? (
                            <Image className="h-6 w-6 text-blue-600" />
                          ) : (
                            <Scissors className="h-6 w-6 text-blue-600" />
                          )}
                        </div>
                        <div>
                          <CardTitle className="text-lg">
                            {service.name}
                          </CardTitle>
                          <CardDescription>
                            {getCategoryName(service)}
                          </CardDescription>
                        </div>
                      </div>
                      <Badge variant={service.is_active ? 'default' : 'secondary'}>
                        {service.is_active ? '启用' : '停用'}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3">
                      {service.description && (
                        <p className="text-sm text-gray-600 line-clamp-2">
                          {service.description}
                        </p>
                      )}
                      
                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div className="flex items-center space-x-2">
                          <Clock className="h-4 w-4 text-gray-500" />
                          <span>{formatDuration(service.duration)}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <DollarSign className="h-4 w-4 text-gray-500" />
                          <span>¥{service.price}</span>
                        </div>
                      </div>
                      
                      {service.vip_price && (
                        <div className="text-sm">
                          <span className="text-gray-500">VIP价格：</span>
                          <span className="font-medium text-green-600">¥{service.vip_price}</span>
                        </div>
                      )}
                      
                      {service.required_skill_level && (
                        <div className="flex items-center space-x-2 text-sm">
                          <Star className="h-4 w-4 text-yellow-500" />
                          <span>要求：{getSkillLevelText(service.required_skill_level)}</span>
                        </div>
                      )}
                      
                      {service.tags && service.tags.length > 0 && (
                        <div className="flex items-center space-x-2">
                          <Tag className="h-4 w-4 text-gray-500" />
                          <div className="flex flex-wrap gap-1">
                            {service.tags.slice(0, 3).map((tag: string, index: number) => (
                              <Badge key={index} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                            {service.tags.length > 3 && (
                              <Badge variant="outline" className="text-xs">
                                +{service.tags.length - 3}
                              </Badge>
                            )}
                          </div>
                        </div>
                      )}
                      
                      {service.cost > 0 && (
                        <div className="text-xs text-gray-500 pt-2 border-t">
                          成本：¥{service.cost} | 利润：¥{(service.price - service.cost).toFixed(2)}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="col-span-full text-center py-12">
                <Scissors className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 text-lg">暂无服务项目</p>
                <p className="text-gray-400 text-sm mt-2">点击"新增服务"按钮添加第一个服务项目</p>
              </div>
            )}
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
