'use client'

import { useEffect, useState } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { createSupabaseClient } from '@/lib/supabase'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { Pencil, Clock, DollarSign, Star, Tag, Image, TrendingUp, Users } from 'lucide-react'

export default function ServiceDetailPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const params = useParams()
  const [service, setService] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [appointments, setAppointments] = useState<any[]>([])
  const [stats, setStats] = useState<any>({})
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  useEffect(() => {
    if (user && params.id) {
      fetchServiceData()
    }
  }, [user, params.id])

  const fetchServiceData = async () => {
    try {
      setIsLoading(true)
      
      // 获取服务基本信息
      const { data: serviceData, error: serviceError } = await supabase
        .from('services')
        .select(`
          *,
          service_categories (id, name, description)
        `)
        .eq('id', params.id)
        .single()

      if (serviceError) {
        console.error('获取服务信息失败:', serviceError)
        router.push('/dashboard/services')
        return
      }

      setService(serviceData)

      // 获取相关预约记录
      const { data: appointmentsData } = await supabase
        .from('appointments')
        .select(`
          *,
          customers (first_name, last_name, phone),
          profiles (full_name)
        `)
        .eq('service_id', params.id)
        .order('appointment_date', { ascending: false })
        .limit(10)

      setAppointments(appointmentsData || [])

      // 获取统计数据
      const { data: statsData } = await supabase.rpc('get_service_stats', {
        service_id: params.id
      })

      if (statsData) {
        setStats(statsData)
      } else {
        // 如果函数不存在，手动计算统计
        const { count: totalAppointments } = await supabase
          .from('appointments')
          .select('*', { count: 'exact', head: true })
          .eq('service_id', params.id)

        const { count: completedAppointments } = await supabase
          .from('appointments')
          .select('*', { count: 'exact', head: true })
          .eq('service_id', params.id)
          .eq('status', 'completed')

        const { data: revenueData } = await supabase
          .from('appointments')
          .select('total_amount')
          .eq('service_id', params.id)
          .eq('status', 'completed')

        const totalRevenue = revenueData?.reduce((sum, apt) => sum + (apt.total_amount || 0), 0) || 0

        setStats({
          total_appointments: totalAppointments || 0,
          completed_appointments: completedAppointments || 0,
          total_revenue: totalRevenue
        })
      }

    } catch (error) {
      console.error('获取服务数据失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${minutes}分钟`
    }
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}小时${remainingMinutes}分钟` : `${hours}小时`
  }

  const getSkillLevelText = (level: number) => {
    const levels = {
      1: '初级',
      2: '中级', 
      3: '高级',
      4: '专家',
      5: '大师'
    }
    return levels[level as keyof typeof levels] || '未设置'
  }

  const getStatusLabel = (status: string) => {
    const statuses = {
      'scheduled': '已预约',
      'in_progress': '进行中',
      'completed': '已完成',
      'cancelled': '已取消',
      'no_show': '爽约'
    }
    return statuses[status as keyof typeof statuses] || status
  }

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
          <p className="mt-4">加载中...</p>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-4">加载服务信息中...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!service) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="text-center py-12">
            <p className="text-gray-500">服务不存在</p>
            <Button 
              className="mt-4" 
              onClick={() => router.push('/dashboard/services')}
            >
              返回服务列表
            </Button>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {service.name}
            </h1>
            <p className="text-gray-600">服务详情</p>
          </div>
          <div className="flex space-x-2">
            <Button 
              variant="outline" 
              onClick={() => router.push('/dashboard/services')}
            >
              返回列表
            </Button>
            <Button onClick={() => router.push(`/dashboard/services/${service.id}/edit`)}>
              <Pencil className="mr-2 h-4 w-4" />
              编辑
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 服务基本信息 */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  服务信息
                  <Badge variant={service.is_active ? 'default' : 'secondary'}>
                    {service.is_active ? '启用' : '停用'}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm text-gray-500">服务分类</p>
                  <p className="font-medium">{service.service_categories?.name || service.category || '未分类'}</p>
                </div>
                
                {service.description && (
                  <div>
                    <p className="text-sm text-gray-500">服务描述</p>
                    <p className="text-sm">{service.description}</p>
                  </div>
                )}
                
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500">服务时长</p>
                    <p className="font-medium">{formatDuration(service.duration)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500">技能要求</p>
                    <p className="font-medium">{getSkillLevelText(service.required_skill_level)}</p>
                  </div>
                </div>
                
                <div className="pt-4 border-t">
                  <div className="grid grid-cols-1 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500">标准价格</p>
                      <p className="text-lg font-semibold text-green-600">¥{service.price}</p>
                    </div>
                    {service.vip_price && (
                      <div>
                        <p className="text-gray-500">VIP价格</p>
                        <p className="font-medium text-blue-600">¥{service.vip_price}</p>
                      </div>
                    )}
                    {service.cost > 0 && (
                      <div>
                        <p className="text-gray-500">成本</p>
                        <p className="font-medium">¥{service.cost}</p>
                        <p className="text-xs text-gray-400">
                          利润：¥{(service.price - service.cost).toFixed(2)}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
                
                {service.tags && service.tags.length > 0 && (
                  <div className="pt-4 border-t">
                    <p className="text-sm text-gray-500 mb-2">服务标签</p>
                    <div className="flex flex-wrap gap-1">
                      {service.tags.map((tag: string, index: number) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {tag}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 统计信息 */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="mr-2 h-5 w-5" />
                  服务统计
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 gap-4">
                  <div className="text-center">
                    <p className="text-2xl font-bold text-blue-600">{stats.total_appointments || 0}</p>
                    <p className="text-sm text-gray-500">总预约数</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-green-600">{stats.completed_appointments || 0}</p>
                    <p className="text-sm text-gray-500">已完成</p>
                  </div>
                  <div className="text-center">
                    <p className="text-2xl font-bold text-purple-600">¥{stats.total_revenue || 0}</p>
                    <p className="text-sm text-gray-500">总营业额</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 预约记录 */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="mr-2 h-5 w-5" />
                  最近预约记录
                </CardTitle>
                <CardDescription>最近10次预约记录</CardDescription>
              </CardHeader>
              <CardContent>
                {appointments.length > 0 ? (
                  <div className="space-y-4">
                    {appointments.map((appointment) => (
                      <div key={appointment.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <p className="font-medium">
                            {appointment.customers?.first_name} {appointment.customers?.last_name}
                          </p>
                          <p className="text-sm text-gray-500">
                            服务员工：{appointment.profiles?.full_name || '未指定'}
                          </p>
                          <p className="text-sm text-gray-500">
                            {new Date(appointment.appointment_date).toLocaleDateString('zh-CN')} {appointment.start_time}
                          </p>
                        </div>
                        <div className="text-right">
                          <Badge variant="outline">
                            {getStatusLabel(appointment.status)}
                          </Badge>
                          {appointment.total_amount && (
                            <p className="text-sm font-medium mt-1">¥{appointment.total_amount}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-8">暂无预约记录</p>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
