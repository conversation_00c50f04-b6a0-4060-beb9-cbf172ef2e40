'use client'

import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { createSupabaseClient } from '@/lib/supabase'
import RevenueChart from '@/components/dashboard/RevenueChart'
import ServiceDistributionChart from '@/components/dashboard/ServiceDistributionChart'
import DashboardLayout from '@/components/layout/DashboardLayout'

export default function DashboardPage() {
  const { user, loading, signOut } = useAuth()
  const router = useRouter()
  const [stats, setStats] = useState({
    totalCustomers: 0,
    totalServices: 0,
    todayAppointments: 0,
    totalRevenue: 0
  })
  const [recentCustomers, setRecentCustomers] = useState<any[]>([])
  const [services, setServices] = useState<any[]>([])
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  useEffect(() => {
    if (user) {
      fetchDashboardData()
    }
  }, [user])

  const fetchDashboardData = async () => {
    try {
      // 获取统计数据
      const statsResponse = await fetch('/api/dashboard/stats')
      if (statsResponse.ok) {
        const { data: statsData } = await statsResponse.json()
        setStats({
          totalCustomers: statsData.total_customers,
          totalServices: statsData.total_services,
          todayAppointments: statsData.today_appointments,
          totalRevenue: statsData.this_month_revenue
        })
      }

      // 获取最近的客户
      const { data: customersData } = await supabase
        .from('customers')
        .select('*')
        .order('created_at', { ascending: false })
        .limit(5)

      // 获取服务列表
      const { data: servicesData } = await supabase
        .from('services')
        .select('*')
        .eq('is_active', true)
        .order('name')

      setRecentCustomers(customersData || [])
      setServices(servicesData || [])
    } catch (error) {
      console.error('获取仪表板数据失败:', error)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
          <p className="mt-4">加载中...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return null
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900">仪表板</h1>
          <p className="text-gray-600">欢迎回来，{user.email}</p>
        </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  客户总数
                </CardTitle>
                <div className="h-4 w-4 text-muted-foreground">
                  👥
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalCustomers}</div>
                <p className="text-xs text-muted-foreground">
                  注册客户数量
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  服务项目
                </CardTitle>
                <div className="h-4 w-4 text-muted-foreground">
                  ✂️
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.totalServices}</div>
                <p className="text-xs text-muted-foreground">
                  可用服务项目
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  今日预约
                </CardTitle>
                <div className="h-4 w-4 text-muted-foreground">
                  📅
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{stats.todayAppointments}</div>
                <p className="text-xs text-muted-foreground">
                  今日预约数量
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  本月营业额
                </CardTitle>
                <div className="h-4 w-4 text-muted-foreground">
                  ¥
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">¥{stats.totalRevenue}</div>
                <p className="text-xs text-muted-foreground">
                  本月总收入
                </p>
              </CardContent>
            </Card>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>最近客户</CardTitle>
                <CardDescription>
                  最新注册的客户
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentCustomers.length > 0 ? (
                    recentCustomers.map((customer) => (
                      <div key={customer.id} className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">{customer.first_name} {customer.last_name}</p>
                          <p className="text-sm text-gray-500">{customer.phone}</p>
                        </div>
                        <div className="text-sm text-gray-500">
                          {customer.customer_type === 'vip' ? 'VIP' : '普通'}
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-gray-500">暂无客户数据</p>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>服务项目</CardTitle>
                <CardDescription>
                  当前可用的服务项目
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {services.length > 0 ? (
                    services.slice(0, 5).map((service) => (
                      <div key={service.id} className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">{service.name}</p>
                          <p className="text-sm text-gray-500">{service.duration}分钟</p>
                        </div>
                        <div className="text-sm font-medium">
                          ¥{service.price}
                        </div>
                      </div>
                    ))
                  ) : (
                    <p className="text-sm text-gray-500">暂无服务项目</p>
                  )}
                  <div className="pt-4 border-t">
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => router.push('/dashboard/customers')}
                    >
                      查看客户管理
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 图表区域 */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8">
            <Card>
              <CardHeader>
                <CardTitle>营业额趋势</CardTitle>
                <CardDescription>
                  近7天营业额和预约数量趋势
                </CardDescription>
              </CardHeader>
              <CardContent>
                <RevenueChart />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>服务分布</CardTitle>
                <CardDescription>
                  各类服务的预约占比
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ServiceDistributionChart />
              </CardContent>
            </Card>
          </div>

          {/* 快速操作区域 */}
          <div className="mt-8">
            <Card>
              <CardHeader>
                <CardTitle>快速操作</CardTitle>
                <CardDescription>
                  常用功能快速入口
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <Button
                    className="h-20 flex flex-col items-center justify-center"
                    onClick={() => router.push('/dashboard/customers/new')}
                  >
                    <span className="text-2xl mb-2">👤</span>
                    新增客户
                  </Button>
                  <Button
                    className="h-20 flex flex-col items-center justify-center"
                    variant="outline"
                    onClick={() => alert('预约管理功能即将推出')}
                  >
                    <span className="text-2xl mb-2">📅</span>
                    新增预约
                  </Button>
                  <Button
                    className="h-20 flex flex-col items-center justify-center"
                    variant="outline"
                    onClick={() => alert('财务管理功能即将推出')}
                  >
                    <span className="text-2xl mb-2">💰</span>
                    财务记录
                  </Button>
                  <Button
                    className="h-20 flex flex-col items-center justify-center"
                    variant="outline"
                    onClick={() => alert('报表功能即将推出')}
                  >
                    <span className="text-2xl mb-2">📊</span>
                    查看报表
                  </Button>
                </div>
              </CardContent>
            </Card>
        </div>
      </div>
    </DashboardLayout>
  )
}
