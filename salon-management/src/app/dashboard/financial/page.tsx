'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { createSupabaseClient } from '@/lib/supabase'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  CreditCard, 
  PieChart,
  BarChart3,
  Plus,
  Wallet
} from 'lucide-react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart as RechartsPieChart, Cell } from 'recharts'

export default function FinancialPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [stats, setStats] = useState<any>({})
  const [transactions, setTransactions] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [period, setPeriod] = useState('month')
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  useEffect(() => {
    if (user) {
      fetchFinancialData()
    }
  }, [user, period])

  const fetchFinancialData = async () => {
    try {
      setIsLoading(true)
      
      // 获取财务统计
      const { data: statsData } = await fetch(`/api/financial/stats?period=${period}`)
        .then(res => res.json())
      
      if (statsData) {
        setStats(statsData)
      }

      // 获取最近交易记录
      const { data: transactionsData, error: transactionsError } = await supabase
        .from('financial_transactions')
        .select(`
          *,
          staff:profiles!staff_id (full_name),
          customer:customers!customer_id (first_name, last_name)
        `)
        .order('transaction_date', { ascending: false })
        .limit(10)

      if (!transactionsError) {
        setTransactions(transactionsData || [])
      }

    } catch (error) {
      console.error('获取财务数据失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getTransactionTypeLabel = (type: string) => {
    const types = {
      'income': '收入',
      'expense': '支出',
      'refund': '退款',
      'commission': '提成'
    }
    return types[type as keyof typeof types] || type
  }

  const getTransactionTypeBadge = (type: string) => {
    switch (type) {
      case 'income':
        return 'default'
      case 'expense':
        return 'destructive'
      case 'refund':
        return 'secondary'
      case 'commission':
        return 'outline'
      default:
        return 'outline'
    }
  }

  const formatCurrency = (amount: number) => {
    return `¥${amount.toFixed(2)}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN')
  }

  // 图表颜色
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8']

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
          <p className="mt-4">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">财务管理</h1>
            <p className="text-gray-600">管理收支、利润分析和会员卡系统</p>
          </div>
          <div className="flex space-x-2">
            <Select value={period} onValueChange={setPeriod}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="day">今日</SelectItem>
                <SelectItem value="week">本周</SelectItem>
                <SelectItem value="month">本月</SelectItem>
                <SelectItem value="year">本年</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={() => router.push('/dashboard/financial/transactions/new')}>
              <Plus className="mr-2 h-4 w-4" />
              记录交易
            </Button>
          </div>
        </div>

        {isLoading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-4">加载财务数据中...</p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* 财务概览卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">总收入</CardTitle>
                  <TrendingUp className="h-4 w-4 text-green-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">
                    {formatCurrency(stats.summary?.total_income || 0)}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    订单收入: {formatCurrency(stats.summary?.order_revenue || 0)}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">总支出</CardTitle>
                  <TrendingDown className="h-4 w-4 text-red-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-red-600">
                    {formatCurrency(stats.summary?.total_expense || 0)}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    运营成本和费用
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">净利润</CardTitle>
                  <DollarSign className="h-4 w-4 text-blue-600" />
                </CardHeader>
                <CardContent>
                  <div className={`text-2xl font-bold ${(stats.summary?.profit || 0) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {formatCurrency(stats.summary?.profit || 0)}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    利润率: {stats.summary?.profit_margin || 0}%
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">会员余额</CardTitle>
                  <Wallet className="h-4 w-4 text-purple-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-purple-600">
                    {formatCurrency(stats.summary?.membership_balance || 0)}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    活跃会员: {stats.summary?.active_memberships || 0}人
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* 图表区域 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 收支趋势图 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChart3 className="mr-2 h-5 w-5" />
                    收支趋势
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {stats.trends && stats.trends.length > 0 ? (
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart data={stats.trends}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip formatter={(value: any) => formatCurrency(value)} />
                        <Line type="monotone" dataKey="income" stroke="#10B981" name="收入" />
                        <Line type="monotone" dataKey="expense" stroke="#EF4444" name="支出" />
                      </LineChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="h-[300px] flex items-center justify-center text-gray-500">
                      暂无趋势数据
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* 支出分类饼图 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <PieChart className="mr-2 h-5 w-5" />
                    支出分类
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {stats.expense_by_category && stats.expense_by_category.length > 0 ? (
                    <ResponsiveContainer width="100%" height={300}>
                      <RechartsPieChart>
                        <Pie
                          data={stats.expense_by_category}
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="amount"
                          label={({ category, percent }: any) => `${category} ${(percent * 100).toFixed(0)}%`}
                        >
                          {stats.expense_by_category.map((entry: any, index: number) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value: any) => formatCurrency(value)} />
                      </RechartsPieChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="h-[300px] flex items-center justify-center text-gray-500">
                      暂无支出数据
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* 最近交易记录 */}
            <Card>
              <CardHeader>
                <CardTitle>最近交易记录</CardTitle>
                <CardDescription>最新的10条财务交易记录</CardDescription>
              </CardHeader>
              <CardContent>
                {transactions.length > 0 ? (
                  <div className="space-y-4">
                    {transactions.map((transaction) => (
                      <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center space-x-4">
                          <div className={`w-2 h-2 rounded-full ${
                            transaction.transaction_type === 'income' ? 'bg-green-500' : 'bg-red-500'
                          }`} />
                          <div>
                            <p className="font-medium">{transaction.category}</p>
                            <p className="text-sm text-gray-500">
                              {transaction.description || '无描述'}
                            </p>
                            <p className="text-xs text-gray-400">
                              {formatDate(transaction.transaction_date)}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge variant={getTransactionTypeBadge(transaction.transaction_type)}>
                            {getTransactionTypeLabel(transaction.transaction_type)}
                          </Badge>
                          <p className={`text-lg font-semibold mt-1 ${
                            transaction.transaction_type === 'income' ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {transaction.transaction_type === 'income' ? '+' : '-'}
                            {formatCurrency(transaction.amount)}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">暂无交易记录</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 快速操作 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button 
                variant="outline" 
                className="h-20"
                onClick={() => router.push('/dashboard/financial/transactions')}
              >
                <div className="text-center">
                  <BarChart3 className="h-6 w-6 mx-auto mb-2" />
                  <span>查看所有交易</span>
                </div>
              </Button>
              
              <Button 
                variant="outline" 
                className="h-20"
                onClick={() => router.push('/dashboard/financial/reports')}
              >
                <div className="text-center">
                  <PieChart className="h-6 w-6 mx-auto mb-2" />
                  <span>财务报表</span>
                </div>
              </Button>
              
              <Button 
                variant="outline" 
                className="h-20"
                onClick={() => router.push('/dashboard/membership')}
              >
                <div className="text-center">
                  <CreditCard className="h-6 w-6 mx-auto mb-2" />
                  <span>会员卡管理</span>
                </div>
              </Button>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
