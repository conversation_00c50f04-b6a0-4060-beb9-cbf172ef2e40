'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { createSupabaseClient } from '@/lib/supabase'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { Calendar, Clock, User, Scissors, Phone } from 'lucide-react'

export default function AppointmentsPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [appointments, setAppointments] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [filterDate, setFilterDate] = useState('')
  const [filterStatus, setFilterStatus] = useState('')
  const [filterStaff, setFilterStaff] = useState('')
  const [staff, setStaff] = useState<any[]>([])
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  useEffect(() => {
    if (user) {
      fetchAppointments()
      fetchStaff()
    }
  }, [user, filterDate, filterStatus, filterStaff])

  const fetchAppointments = async () => {
    try {
      setIsLoading(true)
      let query = supabase
        .from('appointments')
        .select(`
          *,
          customers (id, first_name, last_name, phone),
          profiles (id, full_name),
          services (id, name, price, duration)
        `)
        .order('appointment_date', { ascending: true })

      // 日期过滤
      if (filterDate) {
        query = query.eq('appointment_date', filterDate)
      } else {
        // 默认显示今天及以后的预约
        const today = new Date().toISOString().split('T')[0]
        query = query.gte('appointment_date', today)
      }

      // 状态过滤
      if (filterStatus) {
        query = query.eq('status', filterStatus)
      }

      // 员工过滤
      if (filterStaff) {
        query = query.eq('staff_id', filterStaff)
      }

      const { data, error } = await query.limit(50)

      if (error) {
        console.error('获取预约数据失败:', error)
      } else {
        setAppointments(data || [])
      }
    } catch (error) {
      console.error('获取预约数据失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchStaff = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id, full_name, role')
        .eq('is_active', true)
        .in('role', ['stylist', 'manager', 'super_admin'])
        .order('full_name')

      if (!error) {
        setStaff(data || [])
      }
    } catch (error) {
      console.error('获取员工数据失败:', error)
    }
  }

  const getStatusLabel = (status: string) => {
    const statuses = {
      'scheduled': '已预约',
      'in_progress': '进行中',
      'completed': '已完成',
      'cancelled': '已取消',
      'no_show': '爽约'
    }
    return statuses[status as keyof typeof statuses] || status
  }

  const getStatusBadgeVariant = (status: string) => {
    switch (status) {
      case 'scheduled':
        return 'default'
      case 'in_progress':
        return 'secondary'
      case 'completed':
        return 'outline'
      case 'cancelled':
        return 'destructive'
      case 'no_show':
        return 'destructive'
      default:
        return 'outline'
    }
  }

  const formatTime = (timeString: string) => {
    if (!timeString) return ''
    return timeString.slice(0, 5) // 只显示HH:MM
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return ''
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    })
  }

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
          <p className="mt-4">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">预约管理</h1>
            <p className="text-gray-600">管理客户预约和服务安排</p>
          </div>
          <Button onClick={() => router.push('/dashboard/appointments/new')}>
            新增预约
          </Button>
        </div>

        {/* 过滤器 */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="w-full sm:w-48">
            <Input
              type="date"
              value={filterDate}
              onChange={(e) => setFilterDate(e.target.value)}
              placeholder="选择日期"
            />
          </div>
          <div className="w-full sm:w-48">
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger>
                <SelectValue placeholder="预约状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部状态</SelectItem>
                <SelectItem value="scheduled">已预约</SelectItem>
                <SelectItem value="in_progress">进行中</SelectItem>
                <SelectItem value="completed">已完成</SelectItem>
                <SelectItem value="cancelled">已取消</SelectItem>
                <SelectItem value="no_show">爽约</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="w-full sm:w-48">
            <Select value={filterStaff} onValueChange={setFilterStaff}>
              <SelectTrigger>
                <SelectValue placeholder="服务员工" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部员工</SelectItem>
                {staff.map((member) => (
                  <SelectItem key={member.id} value={member.id}>
                    {member.full_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {isLoading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-4">加载预约数据中...</p>
          </div>
        ) : (
          <div className="space-y-4">
            {appointments.length > 0 ? (
              appointments.map((appointment) => (
                <Card key={appointment.id} className="hover:shadow-lg transition-shadow cursor-pointer"
                      onClick={() => router.push(`/dashboard/appointments/${appointment.id}`)}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex flex-col items-center justify-center w-16 h-16 bg-blue-50 rounded-lg">
                          <Calendar className="h-6 w-6 text-blue-600" />
                          <span className="text-xs text-blue-600 font-medium">
                            {new Date(appointment.appointment_date).getDate()}
                          </span>
                        </div>
                        
                        <div className="flex-1">
                          <div className="flex items-center space-x-2 mb-2">
                            <h3 className="text-lg font-semibold">
                              {appointment.customers?.first_name} {appointment.customers?.last_name}
                            </h3>
                            <Badge variant={getStatusBadgeVariant(appointment.status)}>
                              {getStatusLabel(appointment.status)}
                            </Badge>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-4 gap-2 text-sm text-gray-600">
                            <div className="flex items-center space-x-1">
                              <Calendar className="h-4 w-4" />
                              <span>{formatDate(appointment.appointment_date)}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Clock className="h-4 w-4" />
                              <span>{formatTime(appointment.start_time)} - {formatTime(appointment.end_time)}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <User className="h-4 w-4" />
                              <span>{appointment.profiles?.full_name || '未指定'}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Scissors className="h-4 w-4" />
                              <span>{appointment.services?.name || '未知服务'}</span>
                            </div>
                          </div>
                          
                          {appointment.customers?.phone && (
                            <div className="flex items-center space-x-1 mt-1 text-sm text-gray-600">
                              <Phone className="h-4 w-4" />
                              <span>{appointment.customers.phone}</span>
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="text-right">
                        {appointment.price && (
                          <p className="text-lg font-semibold text-green-600">
                            ¥{appointment.price}
                          </p>
                        )}
                        {appointment.duration_minutes && (
                          <p className="text-sm text-gray-500">
                            {appointment.duration_minutes}分钟
                          </p>
                        )}
                      </div>
                    </div>
                    
                    {appointment.notes && (
                      <div className="mt-3 pt-3 border-t">
                        <p className="text-sm text-gray-600">
                          <span className="font-medium">备注：</span>
                          {appointment.notes}
                        </p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="text-center py-12">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 text-lg">暂无预约记录</p>
                <p className="text-gray-400 text-sm mt-2">点击"新增预约"按钮创建第一个预约</p>
              </div>
            )}
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
