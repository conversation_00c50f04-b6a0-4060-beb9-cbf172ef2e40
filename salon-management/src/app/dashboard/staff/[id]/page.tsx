'use client'

import { useEffect, useState } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { createSupabaseClient } from '@/lib/supabase'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { Pencil, Phone, Mail, Calendar, DollarSign, User, Clock, Award } from 'lucide-react'

export default function StaffDetailPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const params = useParams()
  const [staff, setStaff] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [appointments, setAppointments] = useState<any[]>([])
  const [schedules, setSchedules] = useState<any[]>([])
  const [skills, setSkills] = useState<any[]>([])
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  useEffect(() => {
    if (user && params.id) {
      fetchStaffData()
    }
  }, [user, params.id])

  const fetchStaffData = async () => {
    try {
      setIsLoading(true)
      
      // 获取员工基本信息
      const { data: staffData, error: staffError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', params.id)
        .single()

      if (staffError) {
        console.error('获取员工信息失败:', staffError)
        router.push('/dashboard/staff')
        return
      }

      setStaff(staffData)

      // 获取员工预约记录
      const { data: appointmentsData } = await supabase
        .from('appointments')
        .select(`
          *,
          customers (first_name, last_name, phone),
          services (name, price)
        `)
        .eq('staff_id', params.id)
        .order('appointment_date', { ascending: false })
        .limit(10)

      setAppointments(appointmentsData || [])

      // 获取员工排班记录
      const { data: schedulesData } = await supabase
        .from('schedules')
        .select('*')
        .eq('staff_id', params.id)
        .gte('date', new Date().toISOString().split('T')[0])
        .order('date', { ascending: true })
        .limit(7)

      setSchedules(schedulesData || [])

      // 获取员工技能
      const { data: skillsData } = await supabase
        .from('staff_skills')
        .select(`
          *,
          services (name, category)
        `)
        .eq('staff_id', params.id)

      setSkills(skillsData || [])

    } catch (error) {
      console.error('获取员工数据失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getRoleLabel = (role: string) => {
    const roles = {
      'super_admin': '超级管理员',
      'manager': '店长',
      'receptionist': '前台',
      'stylist': '发型师',
      'assistant': '助理'
    }
    return roles[role as keyof typeof roles] || role
  }

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'destructive'
      case 'manager':
        return 'default'
      case 'stylist':
        return 'secondary'
      case 'receptionist':
        return 'outline'
      case 'assistant':
        return 'outline'
      default:
        return 'outline'
    }
  }

  const getAppointmentStatusLabel = (status: string) => {
    const statuses = {
      'scheduled': '已预约',
      'in_progress': '进行中',
      'completed': '已完成',
      'cancelled': '已取消',
      'no_show': '爽约'
    }
    return statuses[status as keyof typeof statuses] || status
  }

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
          <p className="mt-4">加载中...</p>
        </div>
      </div>
    )
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-4">加载员工信息中...</p>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  if (!staff) {
    return (
      <DashboardLayout>
        <div className="p-6">
          <div className="text-center py-12">
            <p className="text-gray-500">员工不存在</p>
            <Button 
              className="mt-4" 
              onClick={() => router.push('/dashboard/staff')}
            >
              返回员工列表
            </Button>
          </div>
        </div>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {staff.full_name || '未设置姓名'}
            </h1>
            <p className="text-gray-600">员工详情</p>
          </div>
          <div className="flex space-x-2">
            <Button 
              variant="outline" 
              onClick={() => router.push('/dashboard/staff')}
            >
              返回列表
            </Button>
            <Button onClick={() => router.push(`/dashboard/staff/${staff.id}/edit`)}>
              <Pencil className="mr-2 h-4 w-4" />
              编辑
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 员工基本信息 */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  员工信息
                  <div className="flex flex-col space-y-2">
                    <Badge variant={getRoleBadgeVariant(staff.role)}>
                      {getRoleLabel(staff.role)}
                    </Badge>
                    <Badge variant={staff.is_active ? 'default' : 'secondary'}>
                      {staff.is_active ? '在职' : '离职'}
                    </Badge>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center space-x-3">
                  <Mail className="h-4 w-4 text-gray-500" />
                  <span>{staff.email}</span>
                </div>
                
                {staff.phone && (
                  <div className="flex items-center space-x-3">
                    <Phone className="h-4 w-4 text-gray-500" />
                    <span>{staff.phone}</span>
                  </div>
                )}
                
                {staff.hire_date && (
                  <div className="flex items-center space-x-3">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span>入职：{new Date(staff.hire_date).toLocaleDateString('zh-CN')}</span>
                  </div>
                )}
                
                <div className="pt-4 border-t">
                  <div className="grid grid-cols-1 gap-4 text-sm">
                    {staff.salary && (
                      <div>
                        <p className="text-gray-500">基本薪资</p>
                        <p className="font-medium">¥{staff.salary}</p>
                      </div>
                    )}
                    {staff.commission_rate > 0 && (
                      <div>
                        <p className="text-gray-500">提成比例</p>
                        <p className="font-medium">{(staff.commission_rate * 100).toFixed(1)}%</p>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 技能信息 */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Award className="mr-2 h-5 w-5" />
                  专业技能
                </CardTitle>
              </CardHeader>
              <CardContent>
                {skills.length > 0 ? (
                  <div className="space-y-3">
                    {skills.map((skill) => (
                      <div key={skill.id} className="flex items-center justify-between">
                        <span className="text-sm">{skill.services?.name}</span>
                        <div className="flex items-center space-x-2">
                          <div className="flex">
                            {[1, 2, 3, 4, 5].map((star) => (
                              <span
                                key={star}
                                className={`text-xs ${
                                  star <= skill.skill_level ? 'text-yellow-400' : 'text-gray-300'
                                }`}
                              >
                                ★
                              </span>
                            ))}
                          </div>
                          <span className="text-xs text-gray-500">
                            {skill.skill_level}/5
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-sm">暂无技能记录</p>
                )}
              </CardContent>
            </Card>
          </div>

          {/* 预约记录和排班信息 */}
          <div className="lg:col-span-2 space-y-6">
            {/* 最近预约 */}
            <Card>
              <CardHeader>
                <CardTitle>最近预约</CardTitle>
                <CardDescription>最近10次预约记录</CardDescription>
              </CardHeader>
              <CardContent>
                {appointments.length > 0 ? (
                  <div className="space-y-4">
                    {appointments.map((appointment) => (
                      <div key={appointment.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <p className="font-medium">
                            {appointment.customers?.first_name} {appointment.customers?.last_name}
                          </p>
                          <p className="text-sm text-gray-500">
                            {appointment.services?.name || '未知服务'}
                          </p>
                          <p className="text-sm text-gray-500">
                            {new Date(appointment.appointment_date).toLocaleString('zh-CN')}
                          </p>
                        </div>
                        <div className="text-right">
                          <Badge variant="outline">
                            {getAppointmentStatusLabel(appointment.status)}
                          </Badge>
                          {appointment.price && (
                            <p className="text-sm font-medium mt-1">¥{appointment.price}</p>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-8">暂无预约记录</p>
                )}
              </CardContent>
            </Card>

            {/* 近期排班 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="mr-2 h-5 w-5" />
                  近期排班
                </CardTitle>
                <CardDescription>未来7天的排班安排</CardDescription>
              </CardHeader>
              <CardContent>
                {schedules.length > 0 ? (
                  <div className="space-y-4">
                    {schedules.map((schedule) => (
                      <div key={schedule.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <p className="font-medium">
                            {new Date(schedule.date).toLocaleDateString('zh-CN', { 
                              weekday: 'long', 
                              year: 'numeric', 
                              month: 'long', 
                              day: 'numeric' 
                            })}
                          </p>
                          <p className="text-sm text-gray-500">
                            {schedule.start_time} - {schedule.end_time}
                          </p>
                          {schedule.break_start_time && schedule.break_end_time && (
                            <p className="text-sm text-gray-500">
                              休息：{schedule.break_start_time} - {schedule.break_end_time}
                            </p>
                          )}
                        </div>
                        <Badge variant={schedule.is_available ? 'default' : 'secondary'}>
                          {schedule.is_available ? '可用' : '不可用'}
                        </Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-8">暂无排班记录</p>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
