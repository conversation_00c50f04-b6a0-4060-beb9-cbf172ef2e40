'use client'

import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { useEffect } from 'react'
import AddStaffForm from '@/components/staff/AddStaffForm'
import { Button } from '@/components/ui/button'
import DashboardLayout from '@/components/layout/DashboardLayout'

export default function NewStaffPage() {
  const { user, loading } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  const handleSuccess = (staff: any) => {
    router.push('/dashboard/staff')
  }

  const handleCancel = () => {
    router.push('/dashboard/staff')
  }

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
          <p className="mt-4">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">新增员工</h1>
            <p className="text-gray-600">添加新的员工信息</p>
          </div>
          <Button variant="outline" onClick={handleCancel}>
            返回员工列表
          </Button>
        </div>

        <AddStaffForm onSuccess={handleSuccess} onCancel={handleCancel} />
      </div>
    </DashboardLayout>
  )
}
