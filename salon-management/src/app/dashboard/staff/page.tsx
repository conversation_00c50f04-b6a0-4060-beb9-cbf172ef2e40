'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { createSupabaseClient } from '@/lib/supabase'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { User, Phone, Mail, Calendar, DollarSign } from 'lucide-react'

export default function StaffPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [staff, setStaff] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterRole, setFilterRole] = useState('')
  const [filterStatus, setFilterStatus] = useState('')
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  useEffect(() => {
    if (user) {
      fetchStaff()
    }
  }, [user, searchTerm, filterRole, filterStatus])

  const fetchStaff = async () => {
    try {
      setIsLoading(true)
      let query = supabase
        .from('profiles')
        .select('*')
        .order('created_at', { ascending: false })

      // 搜索过滤
      if (searchTerm) {
        query = query.or(`full_name.ilike.%${searchTerm}%,email.ilike.%${searchTerm}%,phone.ilike.%${searchTerm}%`)
      }

      // 角色过滤
      if (filterRole) {
        query = query.eq('role', filterRole)
      }

      // 状态过滤
      if (filterStatus) {
        query = query.eq('is_active', filterStatus === 'active')
      }

      const { data, error } = await query

      if (error) {
        console.error('获取员工数据失败:', error)
      } else {
        setStaff(data || [])
      }
    } catch (error) {
      console.error('获取员工数据失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleSearch = (value: string) => {
    setSearchTerm(value)
  }

  const handleRoleFilter = (value: string) => {
    setFilterRole(value)
  }

  const handleStatusFilter = (value: string) => {
    setFilterStatus(value)
  }

  const getRoleLabel = (role: string) => {
    const roles = {
      'super_admin': '超级管理员',
      'manager': '店长',
      'receptionist': '前台',
      'stylist': '发型师',
      'assistant': '助理'
    }
    return roles[role as keyof typeof roles] || role
  }

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'destructive'
      case 'manager':
        return 'default'
      case 'stylist':
        return 'secondary'
      case 'receptionist':
        return 'outline'
      case 'assistant':
        return 'outline'
      default:
        return 'outline'
    }
  }

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
          <p className="mt-4">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">员工管理</h1>
            <p className="text-gray-600">管理您的员工信息和权限</p>
          </div>
          <Button onClick={() => router.push('/dashboard/staff/new')}>
            新增员工
          </Button>
        </div>

        {/* 搜索和过滤 */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <Input
              placeholder="搜索员工姓名、邮箱或电话..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full"
            />
          </div>
          <div className="w-full sm:w-48">
            <Select value={filterRole} onValueChange={handleRoleFilter}>
              <SelectTrigger>
                <SelectValue placeholder="员工角色" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部角色</SelectItem>
                <SelectItem value="super_admin">超级管理员</SelectItem>
                <SelectItem value="manager">店长</SelectItem>
                <SelectItem value="stylist">发型师</SelectItem>
                <SelectItem value="receptionist">前台</SelectItem>
                <SelectItem value="assistant">助理</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="w-full sm:w-32">
            <Select value={filterStatus} onValueChange={handleStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="状态" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">全部状态</SelectItem>
                <SelectItem value="active">在职</SelectItem>
                <SelectItem value="inactive">离职</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {isLoading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-4">加载员工数据中...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {staff.length > 0 ? (
              staff.map((member) => (
                <Card key={member.id} className="hover:shadow-lg transition-shadow cursor-pointer"
                      onClick={() => router.push(`/dashboard/staff/${member.id}`)}>
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                          <User className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <CardTitle className="text-lg">
                            {member.full_name || '未设置姓名'}
                          </CardTitle>
                          <CardDescription>
                            {member.email}
                          </CardDescription>
                        </div>
                      </div>
                      <div className="flex flex-col items-end space-y-2">
                        <Badge variant={getRoleBadgeVariant(member.role)}>
                          {getRoleLabel(member.role)}
                        </Badge>
                        <Badge variant={member.is_active ? 'default' : 'secondary'}>
                          {member.is_active ? '在职' : '离职'}
                        </Badge>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {member.phone && (
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <Phone className="h-4 w-4" />
                          <span>{member.phone}</span>
                        </div>
                      )}
                      
                      {member.hire_date && (
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <Calendar className="h-4 w-4" />
                          <span>入职：{new Date(member.hire_date).toLocaleDateString('zh-CN')}</span>
                        </div>
                      )}
                      
                      {member.salary && (
                        <div className="flex items-center space-x-2 text-sm text-gray-600">
                          <DollarSign className="h-4 w-4" />
                          <span>薪资：¥{member.salary}</span>
                        </div>
                      )}
                      
                      {member.commission_rate > 0 && (
                        <div className="text-sm text-gray-600">
                          <span>提成比例：{(member.commission_rate * 100).toFixed(1)}%</span>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              <div className="col-span-full text-center py-12">
                <p className="text-gray-500 text-lg">暂无员工数据</p>
                <p className="text-gray-400 text-sm mt-2">点击"新增员工"按钮添加第一个员工</p>
              </div>
            )}
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
