'use client'

import { useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { Loading, PageLoading, CardLoading } from '@/components/ui/loading'
import { EmptyState, NoDataFound } from '@/components/ui/empty-state'
import { SearchInput } from '@/components/ui/search'
import { DataTable } from '@/components/ui/data-table'
import { useToastHelpers } from '@/components/ui/toast'
import { 
  Users, 
  Calendar, 
  Scissors, 
  Package,
  Star,
  Heart,
  Zap
} from 'lucide-react'

export default function UITestPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [searchValue, setSearchValue] = useState('')
  const [showLoading, setShowLoading] = useState(false)
  const toast = useToastHelpers()

  if (loading || !user) {
    return <PageLoading text="加载UI测试页面..." />
  }

  // 测试数据
  const testData = [
    { id: 1, name: '张三', phone: '138-0000-0001', type: 'VIP', status: '活跃' },
    { id: 2, name: '李四', phone: '139-0000-0002', type: '普通', status: '普通' },
    { id: 3, name: '王五', phone: '137-0000-0003', type: 'VIP', status: '流失' },
  ]

  const columns = [
    { key: 'name', title: '姓名' },
    { key: 'phone', title: '电话' },
    { 
      key: 'type', 
      title: '类型',
      render: (value: string) => (
        <Badge variant={value === 'VIP' ? 'default' : 'secondary'}>
          {value}
        </Badge>
      )
    },
    { key: 'status', title: '状态' },
  ]

  return (
    <DashboardLayout>
      <div className="p-6 space-y-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">UI组件测试</h1>
          <p className="text-gray-600">测试和预览所有UI组件</p>
        </div>

        {/* 加载组件测试 */}
        <Card>
          <CardHeader>
            <CardTitle>加载组件</CardTitle>
            <CardDescription>不同类型的加载状态</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <h4 className="text-sm font-medium mb-3">默认加载</h4>
                <Loading />
              </div>
              <div className="text-center">
                <h4 className="text-sm font-medium mb-3">最小化加载</h4>
                <Loading variant="minimal" />
              </div>
              <div className="text-center">
                <h4 className="text-sm font-medium mb-3">品牌化加载</h4>
                <Loading variant="branded" size="sm" />
              </div>
            </div>
            <div>
              <Button 
                onClick={() => {
                  setShowLoading(true)
                  setTimeout(() => setShowLoading(false), 3000)
                }}
              >
                测试卡片加载 (3秒)
              </Button>
              {showLoading && <CardLoading />}
            </div>
          </CardContent>
        </Card>

        {/* 空状态组件测试 */}
        <Card>
          <CardHeader>
            <CardTitle>空状态组件</CardTitle>
            <CardDescription>不同场景的空状态展示</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="border rounded-lg p-4">
                <EmptyState
                  icon={Users}
                  title="暂无客户"
                  description="还没有添加任何客户，点击下方按钮添加第一个客户"
                  action={{
                    label: "添加客户",
                    onClick: () => toast.info("点击了添加客户")
                  }}
                />
              </div>
              <div className="border rounded-lg p-4">
                <NoDataFound 
                  title="没有找到数据"
                  description="请尝试调整搜索条件"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 搜索组件测试 */}
        <Card>
          <CardHeader>
            <CardTitle>搜索组件</CardTitle>
            <CardDescription>智能搜索和过滤</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label>基础搜索</Label>
                <SearchInput
                  value={searchValue}
                  onChange={setSearchValue}
                  placeholder="搜索客户姓名或电话..."
                />
              </div>
              <div>
                <Label>带清除按钮的搜索</Label>
                <SearchInput
                  value={searchValue}
                  onChange={setSearchValue}
                  placeholder="输入关键词搜索..."
                  onClear={() => toast.info("搜索已清除")}
                />
              </div>
            </div>
            {searchValue && (
              <div className="p-3 bg-blue-50 border border-blue-200 rounded-md">
                <p className="text-sm text-blue-800">
                  当前搜索: <strong>{searchValue}</strong>
                </p>
              </div>
            )}
          </CardContent>
        </Card>

        {/* 数据表格测试 */}
        <Card>
          <CardHeader>
            <CardTitle>数据表格</CardTitle>
            <CardDescription>响应式数据表格组件</CardDescription>
          </CardHeader>
          <CardContent>
            <DataTable
              data={testData}
              columns={columns}
              onRowClick={(record) => toast.info(`点击了 ${record.name}`)}
              pagination={{
                current: 1,
                pageSize: 10,
                total: 3,
                onChange: (page, pageSize) => toast.info(`切换到第 ${page} 页，每页 ${pageSize} 条`)
              }}
            />
          </CardContent>
        </Card>

        {/* 通知测试 */}
        <Card>
          <CardHeader>
            <CardTitle>通知系统</CardTitle>
            <CardDescription>不同类型的通知消息</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button 
                onClick={() => toast.success("操作成功", "数据已保存")}
                className="bg-green-600 hover:bg-green-700"
              >
                成功通知
              </Button>
              <Button 
                onClick={() => toast.error("操作失败", "请检查网络连接")}
                variant="destructive"
              >
                错误通知
              </Button>
              <Button 
                onClick={() => toast.warning("注意", "此操作不可撤销")}
                className="bg-yellow-600 hover:bg-yellow-700"
              >
                警告通知
              </Button>
              <Button 
                onClick={() => toast.info("提示", "新功能已上线")}
                variant="outline"
              >
                信息通知
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* 图标和徽章测试 */}
        <Card>
          <CardHeader>
            <CardTitle>图标和徽章</CardTitle>
            <CardDescription>常用的图标和状态徽章</CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h4 className="text-sm font-medium mb-3">图标展示</h4>
              <div className="flex items-center space-x-4">
                <Users className="h-6 w-6 text-blue-600" />
                <Calendar className="h-6 w-6 text-green-600" />
                <Scissors className="h-6 w-6 text-purple-600" />
                <Package className="h-6 w-6 text-orange-600" />
                <Star className="h-6 w-6 text-yellow-600" />
                <Heart className="h-6 w-6 text-red-600" />
                <Zap className="h-6 w-6 text-indigo-600" />
              </div>
            </div>
            <div>
              <h4 className="text-sm font-medium mb-3">徽章样式</h4>
              <div className="flex flex-wrap gap-2">
                <Badge>默认</Badge>
                <Badge variant="secondary">次要</Badge>
                <Badge variant="destructive">危险</Badge>
                <Badge variant="outline">轮廓</Badge>
                <Badge className="bg-green-100 text-green-800">成功</Badge>
                <Badge className="bg-yellow-100 text-yellow-800">警告</Badge>
                <Badge className="bg-blue-100 text-blue-800">信息</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 按钮测试 */}
        <Card>
          <CardHeader>
            <CardTitle>按钮组件</CardTitle>
            <CardDescription>不同样式和状态的按钮</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button>默认按钮</Button>
              <Button variant="secondary">次要按钮</Button>
              <Button variant="outline">轮廓按钮</Button>
              <Button variant="ghost">幽灵按钮</Button>
            </div>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Button size="sm">小按钮</Button>
              <Button size="default">默认大小</Button>
              <Button size="lg">大按钮</Button>
              <Button disabled>禁用按钮</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  )
}
