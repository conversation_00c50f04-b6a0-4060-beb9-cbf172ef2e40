'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { createSupabaseClient } from '@/lib/supabase'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { 
  Package, 
  AlertTriangle, 
  TrendingDown, 
  DollarSign, 
  ShoppingCart,
  Users,
  Plus,
  BarChart3,
  PieChart
} from 'lucide-react'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart as RechartsPieChart, Cell } from 'recharts'

export default function InventoryPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [stats, setStats] = useState<any>({})
  const [isLoading, setIsLoading] = useState(true)
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  useEffect(() => {
    if (user) {
      fetchInventoryData()
    }
  }, [user])

  const fetchInventoryData = async () => {
    try {
      setIsLoading(true)
      
      // 获取库存统计
      const response = await fetch('/api/inventory/stats')
      const result = await response.json()
      
      if (result.data) {
        setStats(result.data)
      }

    } catch (error) {
      console.error('获取库存数据失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return `¥${amount.toFixed(2)}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN')
  }

  const getMovementTypeLabel = (type: string) => {
    const types = {
      'in': '入库',
      'out': '出库',
      'adjustment': '调整',
      'transfer': '调拨'
    }
    return types[type as keyof typeof types] || type
  }

  const getMovementTypeBadge = (type: string) => {
    switch (type) {
      case 'in':
        return 'default'
      case 'out':
        return 'destructive'
      case 'adjustment':
        return 'secondary'
      case 'transfer':
        return 'outline'
      default:
        return 'outline'
    }
  }

  // 图表颜色
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8']

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
          <p className="mt-4">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">库存管理</h1>
            <p className="text-gray-600">管理产品库存、采购和供应商</p>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" onClick={() => router.push('/dashboard/inventory/products')}>
              产品管理
            </Button>
            <Button onClick={() => router.push('/dashboard/inventory/products/new')}>
              <Plus className="mr-2 h-4 w-4" />
              新增产品
            </Button>
          </div>
        </div>

        {isLoading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-4">加载库存数据中...</p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* 库存概览卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">产品总数</CardTitle>
                  <Package className="h-4 w-4 text-blue-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600">
                    {stats.summary?.total_products || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    活跃产品数量
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">低库存警告</CardTitle>
                  <AlertTriangle className="h-4 w-4 text-yellow-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-yellow-600">
                    {stats.summary?.low_stock_count || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    需要补货的产品
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">缺货产品</CardTitle>
                  <TrendingDown className="h-4 w-4 text-red-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-red-600">
                    {stats.summary?.out_of_stock_count || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    库存为零的产品
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">库存总价值</CardTitle>
                  <DollarSign className="h-4 w-4 text-green-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">
                    {formatCurrency(stats.summary?.total_inventory_value || 0)}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    按成本价计算
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">待处理采购</CardTitle>
                  <ShoppingCart className="h-4 w-4 text-purple-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-purple-600">
                    {stats.summary?.pending_purchase_orders || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    待处理的采购订单
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">供应商数量</CardTitle>
                  <Users className="h-4 w-4 text-indigo-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-indigo-600">
                    {stats.summary?.total_suppliers || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    活跃供应商
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* 图表区域 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 库存变动趋势图 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChart3 className="mr-2 h-5 w-5" />
                    库存变动趋势
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {stats.movement_trends && stats.movement_trends.length > 0 ? (
                    <ResponsiveContainer width="100%" height={300}>
                      <LineChart data={stats.movement_trends}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="date" />
                        <YAxis />
                        <Tooltip />
                        <Line type="monotone" dataKey="in" stroke="#10B981" name="入库" />
                        <Line type="monotone" dataKey="out" stroke="#EF4444" name="出库" />
                      </LineChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="h-[300px] flex items-center justify-center text-gray-500">
                      暂无趋势数据
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* 分类库存分布 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <PieChart className="mr-2 h-5 w-5" />
                    分类库存分布
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {stats.category_stats && stats.category_stats.length > 0 ? (
                    <ResponsiveContainer width="100%" height={300}>
                      <RechartsPieChart>
                        <Pie
                          data={stats.category_stats}
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="total_value"
                          label={({ category, percent }: any) => `${category} ${(percent * 100).toFixed(0)}%`}
                        >
                          {stats.category_stats.map((entry: any, index: number) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip formatter={(value: any) => formatCurrency(value)} />
                      </RechartsPieChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="h-[300px] flex items-center justify-center text-gray-500">
                      暂无分类数据
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* 低库存警告 */}
            {stats.low_stock_products && stats.low_stock_products.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center text-yellow-600">
                    <AlertTriangle className="mr-2 h-5 w-5" />
                    低库存警告
                  </CardTitle>
                  <CardDescription>以下产品库存不足，需要及时补货</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {stats.low_stock_products.map((product: any) => (
                      <div key={product.id} className="p-4 border rounded-lg bg-yellow-50">
                        <h4 className="font-medium">{product.name}</h4>
                        <p className="text-sm text-gray-600">
                          当前库存: {product.current_stock}
                        </p>
                        <p className="text-sm text-gray-600">
                          最低库存: {product.min_stock_level}
                        </p>
                        <Badge variant="destructive" className="mt-2">
                          需要补货
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* 最近库存变动 */}
            <Card>
              <CardHeader>
                <CardTitle>最近库存变动</CardTitle>
                <CardDescription>最新的10条库存变动记录</CardDescription>
              </CardHeader>
              <CardContent>
                {stats.recent_movements && stats.recent_movements.length > 0 ? (
                  <div className="space-y-4">
                    {stats.recent_movements.map((movement: any) => (
                      <div key={movement.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center space-x-4">
                          <div className={`w-2 h-2 rounded-full ${
                            movement.movement_type === 'in' ? 'bg-green-500' : 'bg-red-500'
                          }`} />
                          <div>
                            <p className="font-medium">{movement.products?.name}</p>
                            <p className="text-sm text-gray-500">
                              {movement.reason || '无说明'}
                            </p>
                            <p className="text-xs text-gray-400">
                              {formatDate(movement.created_at)}
                            </p>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge variant={getMovementTypeBadge(movement.movement_type)}>
                            {getMovementTypeLabel(movement.movement_type)}
                          </Badge>
                          <p className={`text-lg font-semibold mt-1 ${
                            movement.movement_type === 'in' ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {movement.movement_type === 'in' ? '+' : '-'}
                            {movement.quantity}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">暂无库存变动记录</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 快速操作 */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Button 
                variant="outline" 
                className="h-20"
                onClick={() => router.push('/dashboard/inventory/products')}
              >
                <div className="text-center">
                  <Package className="h-6 w-6 mx-auto mb-2" />
                  <span>产品管理</span>
                </div>
              </Button>
              
              <Button 
                variant="outline" 
                className="h-20"
                onClick={() => router.push('/dashboard/inventory/purchase-orders')}
              >
                <div className="text-center">
                  <ShoppingCart className="h-6 w-6 mx-auto mb-2" />
                  <span>采购管理</span>
                </div>
              </Button>
              
              <Button 
                variant="outline" 
                className="h-20"
                onClick={() => router.push('/dashboard/inventory/suppliers')}
              >
                <div className="text-center">
                  <Users className="h-6 w-6 mx-auto mb-2" />
                  <span>供应商管理</span>
                </div>
              </Button>
              
              <Button 
                variant="outline" 
                className="h-20"
                onClick={() => router.push('/dashboard/inventory/stock-take')}
              >
                <div className="text-center">
                  <BarChart3 className="h-6 w-6 mx-auto mb-2" />
                  <span>库存盘点</span>
                </div>
              </Button>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
