'use client'

import { useEffect, useState } from 'react'
import { useAuth } from '@/contexts/AuthContext'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { createSupabaseClient } from '@/lib/supabase'
import DashboardLayout from '@/components/layout/DashboardLayout'
import { 
  Megaphone, 
  Target, 
  Users, 
  Gift,
  TrendingUp,
  Phone,
  Plus,
  BarChart3,
  PieChart,
  Tag
} from 'lucide-react'
import { PieChart as RechartsPieChart, Cell, ResponsiveContainer, Tooltip, BarChart, Bar, XAxis, YAxis, CartesianGrid } from 'recharts'

export default function MarketingPage() {
  const { user, loading } = useAuth()
  const router = useRouter()
  const [stats, setStats] = useState<any>({})
  const [isLoading, setIsLoading] = useState(true)
  const [period, setPeriod] = useState('month')
  const supabase = createSupabaseClient()

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login')
    }
  }, [user, loading, router])

  useEffect(() => {
    if (user) {
      fetchMarketingData()
    }
  }, [user, period])

  const fetchMarketingData = async () => {
    try {
      setIsLoading(true)
      
      // 获取营销统计
      const response = await fetch(`/api/marketing/stats?period=${period}`)
      const result = await response.json()
      
      if (result.data) {
        setStats(result.data)
      }

    } catch (error) {
      console.error('获取营销数据失败:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return `¥${amount.toFixed(2)}`
  }

  const getCustomerTypeLabel = (type: string) => {
    const types = {
      'potential': '潜在客户',
      'regular': '普通客户',
      'vip': 'VIP客户',
      'lost': '流失客户'
    }
    return types[type as keyof typeof types] || type
  }

  // 图表颜色
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D']

  if (loading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
          <p className="mt-4">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <DashboardLayout>
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">营销管理</h1>
            <p className="text-gray-600">管理促销活动、客户标签和营销活动</p>
          </div>
          <div className="flex space-x-2">
            <Select value={period} onValueChange={setPeriod}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="day">今日</SelectItem>
                <SelectItem value="week">本周</SelectItem>
                <SelectItem value="month">本月</SelectItem>
                <SelectItem value="year">本年</SelectItem>
              </SelectContent>
            </Select>
            <Button onClick={() => router.push('/dashboard/marketing/promotions/new')}>
              <Plus className="mr-2 h-4 w-4" />
              新建促销
            </Button>
          </div>
        </div>

        {isLoading ? (
          <div className="text-center py-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-4">加载营销数据中...</p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* 营销概览卡片 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">促销活动</CardTitle>
                  <Megaphone className="h-4 w-4 text-blue-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600">
                    {stats.summary?.active_promotions || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    总计: {stats.summary?.total_promotions || 0}个
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">促销使用</CardTitle>
                  <Target className="h-4 w-4 text-green-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">
                    {stats.summary?.total_promotion_usage || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    优惠金额: {formatCurrency(stats.summary?.total_discount_amount || 0)}
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">客户回访</CardTitle>
                  <Phone className="h-4 w-4 text-purple-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-purple-600">
                    {stats.summary?.completed_follow_ups || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    完成率: {stats.summary?.follow_up_completion_rate || 0}%
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">推荐奖励</CardTitle>
                  <Gift className="h-4 w-4 text-orange-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-orange-600">
                    {stats.summary?.total_referrals || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    奖励金额: {formatCurrency(stats.summary?.total_referral_rewards || 0)}
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* 图表区域 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* 客户标签分布 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Tag className="mr-2 h-5 w-5" />
                    客户标签分布
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {stats.tag_distribution && stats.tag_distribution.length > 0 ? (
                    <ResponsiveContainer width="100%" height={300}>
                      <RechartsPieChart>
                        <Pie
                          data={stats.tag_distribution}
                          cx="50%"
                          cy="50%"
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="customer_count"
                          label={({ tag_name, percent }: any) => `${tag_name} ${(percent * 100).toFixed(0)}%`}
                        >
                          {stats.tag_distribution.map((entry: any, index: number) => (
                            <Cell key={`cell-${index}`} fill={entry.tag_color || COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip />
                      </RechartsPieChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="h-[300px] flex items-center justify-center text-gray-500">
                      暂无标签数据
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* 客户类型分布 */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Users className="mr-2 h-5 w-5" />
                    客户类型分布
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {stats.customer_type_distribution && stats.customer_type_distribution.length > 0 ? (
                    <ResponsiveContainer width="100%" height={300}>
                      <BarChart data={stats.customer_type_distribution}>
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis 
                          dataKey="customer_type" 
                          tickFormatter={(value) => getCustomerTypeLabel(value)}
                        />
                        <YAxis />
                        <Tooltip 
                          labelFormatter={(value) => getCustomerTypeLabel(value)}
                        />
                        <Bar dataKey="count" fill="#3B82F6" />
                      </BarChart>
                    </ResponsiveContainer>
                  ) : (
                    <div className="h-[300px] flex items-center justify-center text-gray-500">
                      暂无客户类型数据
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>

            {/* 促销活动效果 */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="mr-2 h-5 w-5" />
                  促销活动效果
                </CardTitle>
                <CardDescription>最近5个促销活动的使用情况</CardDescription>
              </CardHeader>
              <CardContent>
                {stats.promotion_effectiveness && stats.promotion_effectiveness.length > 0 ? (
                  <div className="space-y-4">
                    {stats.promotion_effectiveness.map((promotion: any, index: number) => (
                      <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <h4 className="font-medium">{promotion.name}</h4>
                          <p className="text-sm text-gray-500">
                            使用次数: {promotion.usage_count}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-semibold text-green-600">
                            {formatCurrency(promotion.total_discount)}
                          </p>
                          <Badge variant="outline">
                            效果: {promotion.effectiveness}%
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Megaphone className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-500">暂无促销活动数据</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* 快速操作 */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Button 
                variant="outline" 
                className="h-20"
                onClick={() => router.push('/dashboard/marketing/promotions')}
              >
                <div className="text-center">
                  <Megaphone className="h-6 w-6 mx-auto mb-2" />
                  <span>促销管理</span>
                </div>
              </Button>
              
              <Button 
                variant="outline" 
                className="h-20"
                onClick={() => router.push('/dashboard/marketing/customer-tags')}
              >
                <div className="text-center">
                  <Tag className="h-6 w-6 mx-auto mb-2" />
                  <span>客户标签</span>
                </div>
              </Button>
              
              <Button 
                variant="outline" 
                className="h-20"
                onClick={() => router.push('/dashboard/marketing/campaigns')}
              >
                <div className="text-center">
                  <Target className="h-6 w-6 mx-auto mb-2" />
                  <span>营销活动</span>
                </div>
              </Button>
              
              <Button 
                variant="outline" 
                className="h-20"
                onClick={() => router.push('/dashboard/marketing/follow-ups')}
              >
                <div className="text-center">
                  <Phone className="h-6 w-6 mx-auto mb-2" />
                  <span>客户回访</span>
                </div>
              </Button>
            </div>
          </div>
        )}
      </div>
    </DashboardLayout>
  )
}
