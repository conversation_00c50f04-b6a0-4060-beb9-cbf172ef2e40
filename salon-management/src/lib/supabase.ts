import { createClient } from '@supabase/supabase-js'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'

// 客户端Supabase客户端
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// 客户端组件使用的Supabase客户端
export const createSupabaseClient = () => createClientComponentClient()

// 数据库类型定义
export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          role: 'super_admin' | 'manager' | 'receptionist' | 'stylist' | 'assistant'
          phone: string | null
          hire_date: string | null
          salary: number | null
          commission_rate: number | null
          is_active: boolean | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'super_admin' | 'manager' | 'receptionist' | 'stylist' | 'assistant'
          phone?: string | null
          hire_date?: string | null
          salary?: number | null
          commission_rate?: number | null
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'super_admin' | 'manager' | 'receptionist' | 'stylist' | 'assistant'
          phone?: string | null
          hire_date?: string | null
          salary?: number | null
          commission_rate?: number | null
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
      }
      customers: {
        Row: {
          id: string
          first_name: string
          last_name: string
          phone: string
          email: string | null
          date_of_birth: string | null
          gender: 'male' | 'female' | 'other' | null
          address: string | null
          notes: string | null
          avatar_url: string | null
          tags: string[] | null
          customer_type: 'vip' | 'regular' | 'potential' | 'lost' | null
          total_spent: number | null
          visit_count: number | null
          last_visit_date: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          first_name: string
          last_name: string
          phone: string
          email?: string | null
          date_of_birth?: string | null
          gender?: 'male' | 'female' | 'other' | null
          address?: string | null
          notes?: string | null
          avatar_url?: string | null
          tags?: string[] | null
          customer_type?: 'vip' | 'regular' | 'potential' | 'lost' | null
          total_spent?: number | null
          visit_count?: number | null
          last_visit_date?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          first_name?: string
          last_name?: string
          phone?: string
          email?: string | null
          date_of_birth?: string | null
          gender?: 'male' | 'female' | 'other' | null
          address?: string | null
          notes?: string | null
          avatar_url?: string | null
          tags?: string[] | null
          customer_type?: 'vip' | 'regular' | 'potential' | 'lost' | null
          total_spent?: number | null
          visit_count?: number | null
          last_visit_date?: string | null
          created_at?: string
          updated_at?: string
        }
      }
      services: {
        Row: {
          id: string
          name: string
          description: string | null
          duration: number
          price: number
          category: 'haircut' | 'styling' | 'treatment' | 'coloring' | 'other'
          is_active: boolean | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          description?: string | null
          duration: number
          price: number
          category: 'haircut' | 'styling' | 'treatment' | 'coloring' | 'other'
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          description?: string | null
          duration?: number
          price?: number
          category?: 'haircut' | 'styling' | 'treatment' | 'coloring' | 'other'
          is_active?: boolean | null
          created_at?: string
          updated_at?: string
        }
      }
      appointments: {
        Row: {
          id: string
          customer_id: string | null
          staff_id: string | null
          service_id: string | null
          appointment_date: string
          duration_minutes: number | null
          status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'no_show' | null
          notes: string | null
          price: number | null
          actual_start_time: string | null
          actual_end_time: string | null
          created_by: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          customer_id?: string | null
          staff_id?: string | null
          service_id?: string | null
          appointment_date: string
          duration_minutes?: number | null
          status?: 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'no_show' | null
          notes?: string | null
          price?: number | null
          actual_start_time?: string | null
          actual_end_time?: string | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          customer_id?: string | null
          staff_id?: string | null
          service_id?: string | null
          appointment_date?: string
          duration_minutes?: number | null
          status?: 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'no_show' | null
          notes?: string | null
          price?: number | null
          actual_start_time?: string | null
          actual_end_time?: string | null
          created_by?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      user_role: 'super_admin' | 'manager' | 'receptionist' | 'stylist' | 'assistant'
      customer_type: 'vip' | 'regular' | 'potential' | 'lost'
      gender: 'male' | 'female' | 'other'
      appointment_status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'no_show'
      payment_method: 'cash' | 'card' | 'mobile_pay' | 'member_card'
      transaction_type: 'income' | 'expense'
    }
  }
}
