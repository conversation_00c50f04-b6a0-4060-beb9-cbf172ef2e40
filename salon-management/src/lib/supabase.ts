import { createClient } from '@supabase/supabase-js'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'

// 客户端Supabase客户端
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// 客户端组件使用的Supabase客户端
export const createSupabaseClient = () => createClientComponentClient()

// 数据库类型定义
export type Database = {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          avatar_url: string | null
          role: 'super_admin' | 'manager' | 'receptionist' | 'stylist' | 'assistant'
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'super_admin' | 'manager' | 'receptionist' | 'stylist' | 'assistant'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          avatar_url?: string | null
          role?: 'super_admin' | 'manager' | 'receptionist' | 'stylist' | 'assistant'
          created_at?: string
          updated_at?: string
        }
      }
      customers: {
        Row: {
          id: string
          name: string
          phone: string | null
          email: string | null
          birthday: string | null
          gender: 'male' | 'female' | 'other' | null
          address: string | null
          notes: string | null
          avatar_url: string | null
          tags: string[] | null
          customer_type: 'vip' | 'regular' | 'potential' | 'lost'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          phone?: string | null
          email?: string | null
          birthday?: string | null
          gender?: 'male' | 'female' | 'other' | null
          address?: string | null
          notes?: string | null
          avatar_url?: string | null
          tags?: string[] | null
          customer_type?: 'vip' | 'regular' | 'potential' | 'lost'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          phone?: string | null
          email?: string | null
          birthday?: string | null
          gender?: 'male' | 'female' | 'other' | null
          address?: string | null
          notes?: string | null
          avatar_url?: string | null
          tags?: string[] | null
          customer_type?: 'vip' | 'regular' | 'potential' | 'lost'
          created_at?: string
          updated_at?: string
        }
      }
      // 更多表类型定义将在后续添加
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      user_role: 'super_admin' | 'manager' | 'receptionist' | 'stylist' | 'assistant'
      customer_type: 'vip' | 'regular' | 'potential' | 'lost'
      gender: 'male' | 'female' | 'other'
    }
  }
}
