// 测试营销管理功能的脚本
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://hynabcadepehsljpsyzd.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh5bmFiY2FkZXBlaHNsanBzeXpkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3NDY1MzIsImV4cCI6MjA2NjMyMjUzMn0.-C6EGu7CU1UbouowpNC2ra-OAQUMzCtcNvXuRzGUlzY'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testMarketingManagement() {
  console.log('开始测试营销管理功能...')
  
  // 1. 登录
  console.log('\n1. 用户登录...')
  const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'admin123456'
  })
  
  if (signInError) {
    console.error('登录失败:', signInError.message)
    return
  }
  
  console.log('登录成功:', signInData.user?.email)
  
  // 2. 查看客户标签
  console.log('\n2. 查看客户标签...')
  const { data: tags, error: tagsError } = await supabase
    .from('customer_tags')
    .select('*')
    .order('name')
  
  if (tagsError) {
    console.error('获取客户标签失败:', tagsError.message)
  } else {
    console.log('客户标签:', tags?.length, '个')
    tags?.forEach((tag, index) => {
      console.log(`  ${index + 1}. ${tag.name} (${tag.color}) - ${tag.description}`)
    })
  }
  
  // 3. 为客户添加标签
  console.log('\n3. 为客户添加标签...')
  
  // 获取一个客户
  const { data: customers } = await supabase
    .from('customers')
    .select('id, first_name, last_name')
    .limit(1)
  
  if (customers && customers.length > 0 && tags && tags.length > 0) {
    const customer = customers[0]
    const tag = tags[0] // 使用第一个标签
    
    const { data: tagRelation, error: tagRelationError } = await supabase
      .from('customer_tag_relations')
      .insert({
        customer_id: customer.id,
        tag_id: tag.id,
        assigned_by: signInData.user.id
      })
      .select()
      .single()
    
    if (tagRelationError) {
      console.error('添加客户标签失败:', tagRelationError.message)
    } else {
      console.log('客户标签添加成功:', `${customer.first_name} ${customer.last_name} -> ${tag.name}`)
    }
  }
  
  // 4. 创建促销活动
  console.log('\n4. 创建促销活动...')
  const startDate = new Date()
  const endDate = new Date()
  endDate.setDate(endDate.getDate() + 30) // 30天后结束
  
  const testPromotion = {
    name: '测试促销活动',
    description: '这是一个测试的促销活动，新客户享受8折优惠',
    promotion_type: 'discount',
    discount_type: 'percentage',
    discount_value: 20.00, // 8折 = 20%折扣
    min_purchase_amount: 100.00,
    max_discount_amount: 50.00,
    customer_groups: ['新客户'],
    start_date: startDate.toISOString(),
    end_date: endDate.toISOString(),
    usage_limit: 100,
    customer_usage_limit: 1,
    created_by: signInData.user.id
  }
  
  const { data: newPromotion, error: promotionError } = await supabase
    .from('promotions')
    .insert(testPromotion)
    .select()
    .single()
  
  if (promotionError) {
    console.error('创建促销活动失败:', promotionError.message)
  } else {
    console.log('促销活动创建成功:')
    console.log('  名称:', newPromotion.name)
    console.log('  类型:', newPromotion.promotion_type)
    console.log('  折扣:', `${newPromotion.discount_value}%`)
    console.log('  有效期:', `${newPromotion.start_date.split('T')[0]} 至 ${newPromotion.end_date.split('T')[0]}`)
  }
  
  // 5. 创建促销使用记录
  if (newPromotion && customers && customers.length > 0) {
    console.log('\n5. 创建促销使用记录...')
    const { data: promotionUsage, error: usageError } = await supabase
      .from('promotion_usages')
      .insert({
        promotion_id: newPromotion.id,
        customer_id: customers[0].id,
        discount_amount: 20.00,
        created_by: signInData.user.id
      })
      .select()
      .single()
    
    if (usageError) {
      console.error('创建促销使用记录失败:', usageError.message)
    } else {
      console.log('促销使用记录创建成功:', `优惠金额 ¥${promotionUsage.discount_amount}`)
      
      // 更新促销活动使用次数
      await supabase
        .from('promotions')
        .update({ usage_count: 1 })
        .eq('id', newPromotion.id)
    }
  }
  
  // 6. 创建客户回访记录
  console.log('\n6. 创建客户回访记录...')
  if (customers && customers.length > 0) {
    const { data: followUp, error: followUpError } = await supabase
      .from('customer_follow_ups')
      .insert({
        customer_id: customers[0].id,
        follow_up_type: 'phone_call',
        purpose: '满意度调查',
        content: '询问客户对上次服务的满意度，了解改进建议',
        result: '成功',
        status: 'completed',
        assigned_to: signInData.user.id,
        completed_by: signInData.user.id,
        completed_at: new Date().toISOString(),
        created_by: signInData.user.id
      })
      .select()
      .single()
    
    if (followUpError) {
      console.error('创建客户回访失败:', followUpError.message)
    } else {
      console.log('客户回访记录创建成功:', followUp.purpose, '-', followUp.result)
    }
  }
  
  // 7. 创建营销活动
  console.log('\n7. 创建营销活动...')
  const { data: campaign, error: campaignError } = await supabase
    .from('marketing_campaigns')
    .insert({
      name: '测试营销活动',
      description: '针对VIP客户的专属优惠活动',
      campaign_type: 'email',
      target_audience: 'VIP客户群体',
      budget: 1000.00,
      actual_cost: 0.00,
      start_date: startDate.toISOString(),
      end_date: endDate.toISOString(),
      status: 'active',
      success_metrics: '邮件打开率>30%，转化率>5%',
      created_by: signInData.user.id
    })
    .select()
    .single()
  
  if (campaignError) {
    console.error('创建营销活动失败:', campaignError.message)
  } else {
    console.log('营销活动创建成功:', campaign.name, '-', campaign.campaign_type)
  }
  
  // 8. 创建推荐奖励记录
  console.log('\n8. 创建推荐奖励记录...')
  if (customers && customers.length > 0) {
    // 假设有两个客户，一个推荐另一个
    const { data: moreCustomers } = await supabase
      .from('customers')
      .select('id')
      .limit(2)
    
    if (moreCustomers && moreCustomers.length >= 2) {
      const { data: referralReward, error: referralError } = await supabase
        .from('referral_rewards')
        .insert({
          referrer_id: moreCustomers[0].id,
          referee_id: moreCustomers[1].id,
          reward_type: 'cash',
          reward_value: 50.00,
          status: 'approved',
          first_purchase_date: new Date().toISOString(),
          created_by: signInData.user.id
        })
        .select()
        .single()
      
      if (referralError) {
        console.error('创建推荐奖励失败:', referralError.message)
      } else {
        console.log('推荐奖励记录创建成功:', `奖励金额 ¥${referralReward.reward_value}`)
      }
    }
  }
  
  // 9. 获取营销统计
  console.log('\n9. 获取营销统计...')
  
  // 促销活动统计
  const { count: totalPromotions } = await supabase
    .from('promotions')
    .select('*', { count: 'exact', head: true })
  
  const { count: activePromotions } = await supabase
    .from('promotions')
    .select('*', { count: 'exact', head: true })
    .eq('is_active', true)
  
  // 促销使用统计
  const { data: promotionUsages } = await supabase
    .from('promotion_usages')
    .select('discount_amount')
  
  const totalUsage = promotionUsages?.length || 0
  const totalDiscount = promotionUsages?.reduce((sum, usage) => sum + parseFloat(usage.discount_amount), 0) || 0
  
  // 客户标签统计
  const tagStats = await Promise.all(
    (tags || []).map(async (tag) => {
      const { count } = await supabase
        .from('customer_tag_relations')
        .select('*', { count: 'exact', head: true })
        .eq('tag_id', tag.id)
      
      return {
        tag_name: tag.name,
        customer_count: count || 0
      }
    })
  )
  
  console.log('营销统计:')
  console.log(`  促销活动: ${activePromotions}/${totalPromotions} (活跃/总数)`)
  console.log(`  促销使用: ${totalUsage} 次，总优惠: ¥${totalDiscount.toFixed(2)}`)
  console.log('  标签分布:')
  tagStats.forEach(stat => {
    console.log(`    ${stat.tag_name}: ${stat.customer_count} 个客户`)
  })
  
  // 10. 清理测试数据
  console.log('\n10. 清理测试数据...')
  
  // 删除推荐奖励
  await supabase.from('referral_rewards').delete().gte('created_at', new Date(Date.now() - 60000).toISOString())
  
  // 删除营销活动
  if (campaign) {
    await supabase.from('marketing_campaigns').delete().eq('id', campaign.id)
  }
  
  // 删除客户回访
  await supabase.from('customer_follow_ups').delete().gte('created_at', new Date(Date.now() - 60000).toISOString())
  
  // 删除促销使用记录
  await supabase.from('promotion_usages').delete().gte('used_at', new Date(Date.now() - 60000).toISOString())
  
  // 删除促销活动
  if (newPromotion) {
    await supabase.from('promotions').delete().eq('id', newPromotion.id)
  }
  
  // 删除客户标签关联
  await supabase.from('customer_tag_relations').delete().gte('assigned_at', new Date(Date.now() - 60000).toISOString())
  
  console.log('测试数据已清理')
  
  console.log('\n测试完成!')
}

testMarketingManagement().catch(console.error)
