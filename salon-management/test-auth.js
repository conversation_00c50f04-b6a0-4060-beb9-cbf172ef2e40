// 测试认证功能的简单脚本
const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://hynabcadepehsljpsyzd.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh5bmFiY2FkZXBlaHNsanBzeXpkIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA3NDY1MzIsImV4cCI6MjA2NjMyMjUzMn0.-C6EGu7CU1UbouowpNC2ra-OAQUMzCtcNvXuRzGUlzY'

const supabase = createClient(supabaseUrl, supabaseKey)

async function testAuth() {
  console.log('开始测试认证功能...')
  
  // 测试注册
  console.log('\n1. 测试用户注册...')
  const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
    email: '<EMAIL>',
    password: 'admin123456',
    options: {
      data: {
        full_name: '系统管理员'
      }
    }
  })
  
  if (signUpError) {
    console.error('注册失败:', signUpError.message)
  } else {
    console.log('注册成功:', signUpData.user?.email)
  }
  
  // 等待一下
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // 测试登录
  console.log('\n2. 测试用户登录...')
  const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
    email: '<EMAIL>',
    password: 'admin123456'
  })
  
  if (signInError) {
    console.error('登录失败:', signInError.message)
  } else {
    console.log('登录成功:', signInData.user?.email)
    
    // 测试获取用户档案
    console.log('\n3. 测试获取用户档案...')
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', signInData.user.id)
      .single()
    
    if (profileError) {
      console.error('获取档案失败:', profileError.message)
    } else {
      console.log('用户档案:', profile)
    }
    
    // 测试获取客户数据
    console.log('\n4. 测试获取客户数据...')
    const { data: customers, error: customersError } = await supabase
      .from('customers')
      .select('*')
      .limit(5)
    
    if (customersError) {
      console.error('获取客户数据失败:', customersError.message)
    } else {
      console.log('客户数据:', customers?.length, '条记录')
      if (customers && customers.length > 0) {
        console.log('第一个客户:', customers[0])
      }
    }
    
    // 测试获取服务数据
    console.log('\n5. 测试获取服务数据...')
    const { data: services, error: servicesError } = await supabase
      .from('services')
      .select('*')
      .limit(5)
    
    if (servicesError) {
      console.error('获取服务数据失败:', servicesError.message)
    } else {
      console.log('服务数据:', services?.length, '条记录')
      if (services && services.length > 0) {
        console.log('第一个服务:', services[0])
      }
    }
  }
  
  console.log('\n测试完成!')
}

testAuth().catch(console.error)
