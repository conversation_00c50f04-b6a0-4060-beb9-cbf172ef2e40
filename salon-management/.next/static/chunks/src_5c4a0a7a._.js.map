{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo3/salon-management/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo3/salon-management/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo3/salon-management/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;KAXS;AAaT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;MAXS;AAaT,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;MARS;AAUT,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf;MARS", "debugId": null}}, {"offset": {"line": 204, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo3/salon-management/src/components/dashboard/RevenueChart.tsx"], "sourcesContent": ["'use client'\n\nimport { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts'\n\n// 模拟数据 - 在实际应用中这些数据应该从API获取\nconst data = [\n  { name: '周一', revenue: 1200, appointments: 8 },\n  { name: '周二', revenue: 1800, appointments: 12 },\n  { name: '周三', revenue: 2200, appointments: 15 },\n  { name: '周四', revenue: 1600, appointments: 10 },\n  { name: '周五', revenue: 2800, appointments: 18 },\n  { name: '周六', revenue: 3200, appointments: 22 },\n  { name: '周日', revenue: 2400, appointments: 16 },\n]\n\nexport default function RevenueChart() {\n  return (\n    <div className=\"h-80\">\n      <ResponsiveContainer width=\"100%\" height=\"100%\">\n        <LineChart data={data}>\n          <CartesianGrid strokeDasharray=\"3 3\" />\n          <XAxis dataKey=\"name\" />\n          <YAxis />\n          <Tooltip \n            formatter={(value, name) => [\n              name === 'revenue' ? `¥${value}` : value,\n              name === 'revenue' ? '营业额' : '预约数'\n            ]}\n          />\n          <Line \n            type=\"monotone\" \n            dataKey=\"revenue\" \n            stroke=\"#8884d8\" \n            strokeWidth={2}\n            dot={{ fill: '#8884d8' }}\n          />\n          <Line \n            type=\"monotone\" \n            dataKey=\"appointments\" \n            stroke=\"#82ca9d\" \n            strokeWidth={2}\n            dot={{ fill: '#82ca9d' }}\n          />\n        </LineChart>\n      </ResponsiveContainer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFA;;;AAIA,4BAA4B;AAC5B,MAAM,OAAO;IACX;QAAE,MAAM;QAAM,SAAS;QAAM,cAAc;IAAE;IAC7C;QAAE,MAAM;QAAM,SAAS;QAAM,cAAc;IAAG;IAC9C;QAAE,MAAM;QAAM,SAAS;QAAM,cAAc;IAAG;IAC9C;QAAE,MAAM;QAAM,SAAS;QAAM,cAAc;IAAG;IAC9C;QAAE,MAAM;QAAM,SAAS;QAAM,cAAc;IAAG;IAC9C;QAAE,MAAM;QAAM,SAAS;QAAM,cAAc;IAAG;IAC9C;QAAE,MAAM;QAAM,SAAS;QAAM,cAAc;IAAG;CAC/C;AAEc,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;YAAC,OAAM;YAAO,QAAO;sBACvC,cAAA,6LAAC,wJAAA,CAAA,YAAS;gBAAC,MAAM;;kCACf,6LAAC,gKAAA,CAAA,gBAAa;wBAAC,iBAAgB;;;;;;kCAC/B,6LAAC,wJAAA,CAAA,QAAK;wBAAC,SAAQ;;;;;;kCACf,6LAAC,wJAAA,CAAA,QAAK;;;;;kCACN,6LAAC,0JAAA,CAAA,UAAO;wBACN,WAAW,CAAC,OAAO,OAAS;gCAC1B,SAAS,YAAY,CAAC,CAAC,EAAE,OAAO,GAAG;gCACnC,SAAS,YAAY,QAAQ;6BAC9B;;;;;;kCAEH,6LAAC,uJAAA,CAAA,OAAI;wBACH,MAAK;wBACL,SAAQ;wBACR,QAAO;wBACP,aAAa;wBACb,KAAK;4BAAE,MAAM;wBAAU;;;;;;kCAEzB,6LAAC,uJAAA,CAAA,OAAI;wBACH,MAAK;wBACL,SAAQ;wBACR,QAAO;wBACP,aAAa;wBACb,KAAK;4BAAE,MAAM;wBAAU;;;;;;;;;;;;;;;;;;;;;;AAMnC;KAhCwB", "debugId": null}}, {"offset": {"line": 349, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo3/salon-management/src/components/dashboard/ServiceDistributionChart.tsx"], "sourcesContent": ["'use client'\n\nimport { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Responsive<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Legend } from 'recharts'\n\n// 模拟数据\nconst data = [\n  { name: '洗剪吹', value: 35, color: '#0088FE' },\n  { name: '染发', value: 25, color: '#00C49F' },\n  { name: '烫发', value: 20, color: '#FFBB28' },\n  { name: '护理', value: 15, color: '#FF8042' },\n  { name: '其他', value: 5, color: '#8884D8' },\n]\n\nexport default function ServiceDistributionChart() {\n  return (\n    <div className=\"h-80\">\n      <ResponsiveContainer width=\"100%\" height=\"100%\">\n        <PieChart>\n          <Pie\n            data={data}\n            cx=\"50%\"\n            cy=\"50%\"\n            labelLine={false}\n            label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}\n            outerRadius={80}\n            fill=\"#8884d8\"\n            dataKey=\"value\"\n          >\n            {data.map((entry, index) => (\n              <Cell key={`cell-${index}`} fill={entry.color} />\n            ))}\n          </Pie>\n          <Tooltip formatter={(value) => [`${value}%`, '占比']} />\n          <Legend />\n        </PieChart>\n      </ResponsiveContainer>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFA;;;AAIA,OAAO;AACP,MAAM,OAAO;IACX;QAAE,MAAM;QAAO,OAAO;QAAI,OAAO;IAAU;IAC3C;QAAE,MAAM;QAAM,OAAO;QAAI,OAAO;IAAU;IAC1C;QAAE,MAAM;QAAM,OAAO;QAAI,OAAO;IAAU;IAC1C;QAAE,MAAM;QAAM,OAAO;QAAI,OAAO;IAAU;IAC1C;QAAE,MAAM;QAAM,OAAO;QAAG,OAAO;IAAU;CAC1C;AAEc,SAAS;IACtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC,sKAAA,CAAA,sBAAmB;YAAC,OAAM;YAAO,QAAO;sBACvC,cAAA,6LAAC,uJAAA,CAAA,WAAQ;;kCACP,6LAAC,kJAAA,CAAA,MAAG;wBACF,MAAM;wBACN,IAAG;wBACH,IAAG;wBACH,WAAW;wBACX,OAAO,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,GAAK,GAAG,KAAK,CAAC,EAAE,CAAC,UAAU,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,CAAC;wBACtE,aAAa;wBACb,MAAK;wBACL,SAAQ;kCAEP,KAAK,GAAG,CAAC,CAAC,OAAO,sBAChB,6LAAC,uJAAA,CAAA,OAAI;gCAAuB,MAAM,MAAM,KAAK;+BAAlC,CAAC,KAAK,EAAE,OAAO;;;;;;;;;;kCAG9B,6LAAC,0JAAA,CAAA,UAAO;wBAAC,WAAW,CAAC,QAAU;gCAAC,GAAG,MAAM,CAAC,CAAC;gCAAE;6BAAK;;;;;;kCAClD,6LAAC,yJAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;AAKjB;KAzBwB", "debugId": null}}, {"offset": {"line": 463, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo3/salon-management/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useRouter, usePathname } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\nimport { cn } from '@/lib/utils'\nimport { \n  Home, \n  Users, \n  Calendar, \n  Scissors, \n  DollarSign, \n  Package, \n  TrendingUp,\n  Settings,\n  LogOut\n} from 'lucide-react'\nimport { useAuth } from '@/contexts/AuthContext'\n\nconst navigation = [\n  { name: '仪表板', href: '/dashboard', icon: Home },\n  { name: '客户管理', href: '/dashboard/customers', icon: Users },\n  { name: '预约管理', href: '/dashboard/appointments', icon: Calendar },\n  { name: '服务项目', href: '/dashboard/services', icon: Scissors },\n  { name: '财务管理', href: '/dashboard/finance', icon: DollarSign },\n  { name: '库存管理', href: '/dashboard/inventory', icon: Package },\n  { name: '营销管理', href: '/dashboard/marketing', icon: TrendingUp },\n  { name: '系统设置', href: '/dashboard/settings', icon: Settings },\n]\n\nexport default function Sidebar() {\n  const router = useRouter()\n  const pathname = usePathname()\n  const { user, signOut } = useAuth()\n\n  const handleSignOut = async () => {\n    await signOut()\n    router.push('/login')\n  }\n\n  return (\n    <div className=\"flex flex-col h-full bg-white border-r border-gray-200\">\n      {/* Logo */}\n      <div className=\"flex items-center justify-center h-16 px-4 border-b border-gray-200\">\n        <h1 className=\"text-xl font-bold text-gray-900\">发廊管理</h1>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"flex-1 px-4 py-6 space-y-2\">\n        {navigation.map((item) => {\n          const isActive = pathname === item.href || pathname.startsWith(item.href + '/')\n          return (\n            <Button\n              key={item.name}\n              variant={isActive ? 'default' : 'ghost'}\n              className={cn(\n                'w-full justify-start',\n                isActive && 'bg-blue-50 text-blue-700 hover:bg-blue-100'\n              )}\n              onClick={() => router.push(item.href)}\n            >\n              <item.icon className=\"mr-3 h-4 w-4\" />\n              {item.name}\n            </Button>\n          )\n        })}\n      </nav>\n\n      {/* User info and logout */}\n      <div className=\"p-4 border-t border-gray-200\">\n        <div className=\"flex items-center mb-3\">\n          <div className=\"flex-1\">\n            <p className=\"text-sm font-medium text-gray-900\">\n              {user?.email}\n            </p>\n            <p className=\"text-xs text-gray-500\">\n              系统用户\n            </p>\n          </div>\n        </div>\n        <Button\n          variant=\"outline\"\n          className=\"w-full justify-start\"\n          onClick={handleSignOut}\n        >\n          <LogOut className=\"mr-3 h-4 w-4\" />\n          退出登录\n        </Button>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAWA;;;AAhBA;;;;;;AAkBA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAO,MAAM;QAAc,MAAM,sMAAA,CAAA,OAAI;IAAC;IAC9C;QAAE,MAAM;QAAQ,MAAM;QAAwB,MAAM,uMAAA,CAAA,QAAK;IAAC;IAC1D;QAAE,MAAM;QAAQ,MAAM;QAA2B,MAAM,6MAAA,CAAA,WAAQ;IAAC;IAChE;QAAE,MAAM;QAAQ,MAAM;QAAuB,MAAM,6MAAA,CAAA,WAAQ;IAAC;IAC5D;QAAE,MAAM;QAAQ,MAAM;QAAsB,MAAM,qNAAA,CAAA,aAAU;IAAC;IAC7D;QAAE,MAAM;QAAQ,MAAM;QAAwB,MAAM,2MAAA,CAAA,UAAO;IAAC;IAC5D;QAAE,MAAM;QAAQ,MAAM;QAAwB,MAAM,qNAAA,CAAA,aAAU;IAAC;IAC/D;QAAE,MAAM;QAAQ,MAAM;QAAuB,MAAM,6MAAA,CAAA,WAAQ;IAAC;CAC7D;AAEc,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEhC,MAAM,gBAAgB;QACpB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAG,WAAU;8BAAkC;;;;;;;;;;;0BAIlD,6LAAC;gBAAI,WAAU;0BACZ,WAAW,GAAG,CAAC,CAAC;oBACf,MAAM,WAAW,aAAa,KAAK,IAAI,IAAI,SAAS,UAAU,CAAC,KAAK,IAAI,GAAG;oBAC3E,qBACE,6LAAC,qIAAA,CAAA,SAAM;wBAEL,SAAS,WAAW,YAAY;wBAChC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wBACA,YAAY;wBAEd,SAAS,IAAM,OAAO,IAAI,CAAC,KAAK,IAAI;;0CAEpC,6LAAC,KAAK,IAAI;gCAAC,WAAU;;;;;;4BACpB,KAAK,IAAI;;uBATL,KAAK,IAAI;;;;;gBAYpB;;;;;;0BAIF,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CACV,MAAM;;;;;;8CAET,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAKzC,6LAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,WAAU;wBACV,SAAS;;0CAET,6LAAC,6MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;AAM7C;GA7DwB;;QACP,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;QACF,kIAAA,CAAA,UAAO;;;KAHX", "debugId": null}}, {"offset": {"line": 672, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo3/salon-management/src/components/layout/DashboardLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Sidebar from './Sidebar'\nimport { Button } from '@/components/ui/button'\nimport { Menu, X } from 'lucide-react'\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode\n}\n\nexport default function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false)\n\n  return (\n    <div className=\"flex h-screen bg-gray-50\">\n      {/* Mobile sidebar overlay */}\n      {sidebarOpen && (\n        <div \n          className=\"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div className={`\n        fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0\n        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}\n      `}>\n        <Sidebar />\n      </div>\n\n      {/* Main content */}\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\n        {/* Mobile header */}\n        <div className=\"lg:hidden flex items-center justify-between p-4 bg-white border-b border-gray-200\">\n          <Button\n            variant=\"ghost\"\n            size=\"sm\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Menu className=\"h-6 w-6\" />\n          </Button>\n          <h1 className=\"text-lg font-semibold\">发廊管理系统</h1>\n          <div className=\"w-10\" /> {/* Spacer */}\n        </div>\n\n        {/* Page content */}\n        <main className=\"flex-1 overflow-auto\">\n          {children}\n        </main>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAWe,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;;IACxE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qBACE,6LAAC;QAAI,WAAU;;YAEZ,6BACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAKlC,6LAAC;gBAAI,WAAW,CAAC;;QAEf,EAAE,cAAc,kBAAkB,oBAAoB;MACxD,CAAC;0BACC,cAAA,6LAAC,0IAAA,CAAA,UAAO;;;;;;;;;;0BAIV,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,eAAe;0CAE9B,cAAA,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,6LAAC;gCAAG,WAAU;0CAAwB;;;;;;0CACtC,6LAAC;gCAAI,WAAU;;;;;;4BAAS;;;;;;;kCAI1B,6LAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX;GA3CwB;KAAA", "debugId": null}}, {"offset": {"line": 794, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo3/salon-management/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuth } from '@/contexts/AuthContext'\nimport { useRouter } from 'next/navigation'\nimport { useEffect, useState } from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { createSupabaseClient } from '@/lib/supabase'\nimport RevenueChart from '@/components/dashboard/RevenueChart'\nimport ServiceDistributionChart from '@/components/dashboard/ServiceDistributionChart'\nimport DashboardLayout from '@/components/layout/DashboardLayout'\n\nexport default function DashboardPage() {\n  const { user, loading, signOut } = useAuth()\n  const router = useRouter()\n  const [stats, setStats] = useState({\n    totalCustomers: 0,\n    totalServices: 0,\n    todayAppointments: 0,\n    totalRevenue: 0\n  })\n  const [recentCustomers, setRecentCustomers] = useState<any[]>([])\n  const [services, setServices] = useState<any[]>([])\n  const supabase = createSupabaseClient()\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/login')\n    }\n  }, [user, loading, router])\n\n  useEffect(() => {\n    if (user) {\n      fetchDashboardData()\n    }\n  }, [user])\n\n  const fetchDashboardData = async () => {\n    try {\n      // 获取统计数据\n      const statsResponse = await fetch('/api/dashboard/stats')\n      if (statsResponse.ok) {\n        const { data: statsData } = await statsResponse.json()\n        setStats({\n          totalCustomers: statsData.total_customers,\n          totalServices: statsData.total_services,\n          todayAppointments: statsData.today_appointments,\n          totalRevenue: statsData.this_month_revenue\n        })\n      }\n\n      // 获取最近的客户\n      const { data: customersData } = await supabase\n        .from('customers')\n        .select('*')\n        .order('created_at', { ascending: false })\n        .limit(5)\n\n      // 获取服务列表\n      const { data: servicesData } = await supabase\n        .from('services')\n        .select('*')\n        .eq('is_active', true)\n        .order('name')\n\n      setRecentCustomers(customersData || [])\n      setServices(servicesData || [])\n    } catch (error) {\n      console.error('获取仪表板数据失败:', error)\n    }\n  }\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900\"></div>\n          <p className=\"mt-4\">加载中...</p>\n        </div>\n      </div>\n    )\n  }\n\n  if (!user) {\n    return null\n  }\n\n  return (\n    <DashboardLayout>\n      <div className=\"p-6\">\n        <div className=\"mb-6\">\n          <h1 className=\"text-3xl font-bold text-gray-900\">仪表板</h1>\n          <p className=\"text-gray-600\">欢迎回来，{user.email}</p>\n        </div>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">\n                  客户总数\n                </CardTitle>\n                <div className=\"h-4 w-4 text-muted-foreground\">\n                  👥\n                </div>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">{stats.totalCustomers}</div>\n                <p className=\"text-xs text-muted-foreground\">\n                  注册客户数量\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">\n                  服务项目\n                </CardTitle>\n                <div className=\"h-4 w-4 text-muted-foreground\">\n                  ✂️\n                </div>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">{stats.totalServices}</div>\n                <p className=\"text-xs text-muted-foreground\">\n                  可用服务项目\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">\n                  今日预约\n                </CardTitle>\n                <div className=\"h-4 w-4 text-muted-foreground\">\n                  📅\n                </div>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">{stats.todayAppointments}</div>\n                <p className=\"text-xs text-muted-foreground\">\n                  今日预约数量\n                </p>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n                <CardTitle className=\"text-sm font-medium\">\n                  本月营业额\n                </CardTitle>\n                <div className=\"h-4 w-4 text-muted-foreground\">\n                  ¥\n                </div>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">¥{stats.totalRevenue}</div>\n                <p className=\"text-xs text-muted-foreground\">\n                  本月总收入\n                </p>\n              </CardContent>\n            </Card>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n            <Card>\n              <CardHeader>\n                <CardTitle>最近客户</CardTitle>\n                <CardDescription>\n                  最新注册的客户\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  {recentCustomers.length > 0 ? (\n                    recentCustomers.map((customer) => (\n                      <div key={customer.id} className=\"flex items-center justify-between\">\n                        <div>\n                          <p className=\"font-medium\">{customer.first_name} {customer.last_name}</p>\n                          <p className=\"text-sm text-gray-500\">{customer.phone}</p>\n                        </div>\n                        <div className=\"text-sm text-gray-500\">\n                          {customer.customer_type === 'vip' ? 'VIP' : '普通'}\n                        </div>\n                      </div>\n                    ))\n                  ) : (\n                    <p className=\"text-sm text-gray-500\">暂无客户数据</p>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <CardTitle>服务项目</CardTitle>\n                <CardDescription>\n                  当前可用的服务项目\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"space-y-4\">\n                  {services.length > 0 ? (\n                    services.slice(0, 5).map((service) => (\n                      <div key={service.id} className=\"flex items-center justify-between\">\n                        <div>\n                          <p className=\"font-medium\">{service.name}</p>\n                          <p className=\"text-sm text-gray-500\">{service.duration}分钟</p>\n                        </div>\n                        <div className=\"text-sm font-medium\">\n                          ¥{service.price}\n                        </div>\n                      </div>\n                    ))\n                  ) : (\n                    <p className=\"text-sm text-gray-500\">暂无服务项目</p>\n                  )}\n                  <div className=\"pt-4 border-t\">\n                    <Button\n                      variant=\"outline\"\n                      className=\"w-full\"\n                      onClick={() => router.push('/dashboard/customers')}\n                    >\n                      查看客户管理\n                    </Button>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* 图表区域 */}\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mt-8\">\n            <Card>\n              <CardHeader>\n                <CardTitle>营业额趋势</CardTitle>\n                <CardDescription>\n                  近7天营业额和预约数量趋势\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <RevenueChart />\n              </CardContent>\n            </Card>\n\n            <Card>\n              <CardHeader>\n                <CardTitle>服务分布</CardTitle>\n                <CardDescription>\n                  各类服务的预约占比\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <ServiceDistributionChart />\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* 快速操作区域 */}\n          <div className=\"mt-8\">\n            <Card>\n              <CardHeader>\n                <CardTitle>快速操作</CardTitle>\n                <CardDescription>\n                  常用功能快速入口\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                  <Button\n                    className=\"h-20 flex flex-col items-center justify-center\"\n                    onClick={() => router.push('/dashboard/customers/new')}\n                  >\n                    <span className=\"text-2xl mb-2\">👤</span>\n                    新增客户\n                  </Button>\n                  <Button\n                    className=\"h-20 flex flex-col items-center justify-center\"\n                    variant=\"outline\"\n                    onClick={() => alert('预约管理功能即将推出')}\n                  >\n                    <span className=\"text-2xl mb-2\">📅</span>\n                    新增预约\n                  </Button>\n                  <Button\n                    className=\"h-20 flex flex-col items-center justify-center\"\n                    variant=\"outline\"\n                    onClick={() => alert('财务管理功能即将推出')}\n                  >\n                    <span className=\"text-2xl mb-2\">💰</span>\n                    财务记录\n                  </Button>\n                  <Button\n                    className=\"h-20 flex flex-col items-center justify-center\"\n                    variant=\"outline\"\n                    onClick={() => alert('报表功能即将推出')}\n                  >\n                    <span className=\"text-2xl mb-2\">📊</span>\n                    查看报表\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n        </div>\n      </div>\n    </DashboardLayout>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAYe,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACzC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACjC,gBAAgB;QAChB,eAAe;QACf,mBAAmB;QACnB,cAAc;IAChB;IACA,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAChE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAClD,MAAM,WAAW,CAAA,GAAA,yHAAA,CAAA,uBAAoB,AAAD;IAEpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,WAAW,CAAC,MAAM;gBACrB,OAAO,IAAI,CAAC;YACd;QACF;kCAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,MAAM;gBACR;YACF;QACF;kCAAG;QAAC;KAAK;IAET,MAAM,qBAAqB;QACzB,IAAI;YACF,SAAS;YACT,MAAM,gBAAgB,MAAM,MAAM;YAClC,IAAI,cAAc,EAAE,EAAE;gBACpB,MAAM,EAAE,MAAM,SAAS,EAAE,GAAG,MAAM,cAAc,IAAI;gBACpD,SAAS;oBACP,gBAAgB,UAAU,eAAe;oBACzC,eAAe,UAAU,cAAc;oBACvC,mBAAmB,UAAU,kBAAkB;oBAC/C,cAAc,UAAU,kBAAkB;gBAC5C;YACF;YAEA,UAAU;YACV,MAAM,EAAE,MAAM,aAAa,EAAE,GAAG,MAAM,SACnC,IAAI,CAAC,aACL,MAAM,CAAC,KACP,KAAK,CAAC,cAAc;gBAAE,WAAW;YAAM,GACvC,KAAK,CAAC;YAET,SAAS;YACT,MAAM,EAAE,MAAM,YAAY,EAAE,GAAG,MAAM,SAClC,IAAI,CAAC,YACL,MAAM,CAAC,KACP,EAAE,CAAC,aAAa,MAChB,KAAK,CAAC;YAET,mBAAmB,iBAAiB,EAAE;YACtC,YAAY,gBAAgB,EAAE;QAChC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,cAAc;QAC9B;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAE,WAAU;kCAAO;;;;;;;;;;;;;;;;;IAI5B;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,6LAAC,kJAAA,CAAA,UAAe;kBACd,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAmC;;;;;;sCACjD,6LAAC;4BAAE,WAAU;;gCAAgB;gCAAM,KAAK,KAAK;;;;;;;;;;;;;8BAE7C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,6LAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAIjD,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;sDAAsB,MAAM,cAAc;;;;;;sDACzD,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,6LAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAIjD,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;sDAAsB,MAAM,aAAa;;;;;;sDACxD,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,6LAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAIjD,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;sDAAsB,MAAM,iBAAiB;;;;;;sDAC5D,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;sCAMjD,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;sDAG3C,6LAAC;4CAAI,WAAU;sDAAgC;;;;;;;;;;;;8CAIjD,6LAAC,mIAAA,CAAA,cAAW;;sDACV,6LAAC;4CAAI,WAAU;;gDAAqB;gDAAE,MAAM,YAAY;;;;;;;sDACxD,6LAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;;;;;;;;;;;;;8BAOnD,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;kDACZ,gBAAgB,MAAM,GAAG,IACxB,gBAAgB,GAAG,CAAC,CAAC,yBACnB,6LAAC;gDAAsB,WAAU;;kEAC/B,6LAAC;;0EACC,6LAAC;gEAAE,WAAU;;oEAAe,SAAS,UAAU;oEAAC;oEAAE,SAAS,SAAS;;;;;;;0EACpE,6LAAC;gEAAE,WAAU;0EAAyB,SAAS,KAAK;;;;;;;;;;;;kEAEtD,6LAAC;wDAAI,WAAU;kEACZ,SAAS,aAAa,KAAK,QAAQ,QAAQ;;;;;;;+CANtC,SAAS,EAAE;;;;sEAWvB,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;sCAM7C,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC;wCAAI,WAAU;;4CACZ,SAAS,MAAM,GAAG,IACjB,SAAS,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,wBACxB,6LAAC;oDAAqB,WAAU;;sEAC9B,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAe,QAAQ,IAAI;;;;;;8EACxC,6LAAC;oEAAE,WAAU;;wEAAyB,QAAQ,QAAQ;wEAAC;;;;;;;;;;;;;sEAEzD,6LAAC;4DAAI,WAAU;;gEAAsB;gEACjC,QAAQ,KAAK;;;;;;;;mDANT,QAAQ,EAAE;;;;0EAWtB,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;0DAEvC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,WAAU;oDACV,SAAS,IAAM,OAAO,IAAI,CAAC;8DAC5B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAUX,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,kJAAA,CAAA,UAAY;;;;;;;;;;;;;;;;sCAIjB,6LAAC,mIAAA,CAAA,OAAI;;8CACH,6LAAC,mIAAA,CAAA,aAAU;;sDACT,6LAAC,mIAAA,CAAA,YAAS;sDAAC;;;;;;sDACX,6LAAC,mIAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,6LAAC,mIAAA,CAAA,cAAW;8CACV,cAAA,6LAAC,8JAAA,CAAA,UAAwB;;;;;;;;;;;;;;;;;;;;;;8BAM/B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;;kDACT,6LAAC,mIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,6LAAC,mIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,WAAU;4CACV,SAAS,IAAM,OAAO,IAAI,CAAC;;8DAE3B,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;gDAAS;;;;;;;sDAG3C,6LAAC,qIAAA,CAAA,SAAM;4CACL,WAAU;4CACV,SAAQ;4CACR,SAAS,IAAM,MAAM;;8DAErB,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;gDAAS;;;;;;;sDAG3C,6LAAC,qIAAA,CAAA,SAAM;4CACL,WAAU;4CACV,SAAQ;4CACR,SAAS,IAAM,MAAM;;8DAErB,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;gDAAS;;;;;;;sDAG3C,6LAAC,qIAAA,CAAA,SAAM;4CACL,WAAU;4CACV,SAAQ;4CACR,SAAS,IAAM,MAAM;;8DAErB,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;gDAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7D;GAvSwB;;QACa,kIAAA,CAAA,UAAO;QAC3B,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}