{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { createMiddlewareClient } from '@supabase/auth-helpers-nextjs'\nimport { NextResponse } from 'next/server'\nimport type { NextRequest } from 'next/server'\n\nexport async function middleware(req: NextRequest) {\n  const res = NextResponse.next()\n  const supabase = createMiddlewareClient({ req, res })\n\n  const {\n    data: { session },\n  } = await supabase.auth.getSession()\n\n  // 如果用户未登录且访问受保护的路由，重定向到登录页面\n  if (!session && req.nextUrl.pathname.startsWith('/dashboard')) {\n    return NextResponse.redirect(new URL('/login', req.url))\n  }\n\n  // 如果用户已登录且访问登录或注册页面，重定向到仪表板\n  if (session && (req.nextUrl.pathname === '/login' || req.nextUrl.pathname === '/register')) {\n    return NextResponse.redirect(new URL('/dashboard', req.url))\n  }\n\n  return res\n}\n\nexport const config = {\n  matcher: ['/dashboard/:path*', '/login', '/register']\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;;;AAGO,eAAe,WAAW,GAAgB;IAC/C,MAAM,MAAM,6LAAA,CAAA,eAAY,CAAC,IAAI;IAC7B,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,yBAAsB,AAAD,EAAE;QAAE;QAAK;IAAI;IAEnD,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;IAElC,4BAA4B;IAC5B,IAAI,CAAC,WAAW,IAAI,OAAO,CAAC,QAAQ,CAAC,UAAU,CAAC,eAAe;QAC7D,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,UAAU,IAAI,GAAG;IACxD;IAEA,4BAA4B;IAC5B,IAAI,WAAW,CAAC,IAAI,OAAO,CAAC,QAAQ,KAAK,YAAY,IAAI,OAAO,CAAC,QAAQ,KAAK,WAAW,GAAG;QAC1F,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC,IAAI,IAAI,cAAc,IAAI,GAAG;IAC5D;IAEA,OAAO;AACT;AAEO,MAAM,SAAS;IACpB,SAAS;QAAC;QAAqB;QAAU;KAAY;AACvD"}}]}