{"version": 3, "sources": [], "sections": [{"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo3/salon-management/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\nimport { createClientComponentClient } from '@supabase/auth-helpers-nextjs'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'\n\n// 客户端Supabase客户端\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// 客户端组件使用的Supabase客户端\nexport const createSupabaseClient = () => createClientComponentClient()\n\n// 数据库类型定义\nexport type Database = {\n  public: {\n    Tables: {\n      profiles: {\n        Row: {\n          id: string\n          email: string\n          full_name: string | null\n          avatar_url: string | null\n          role: 'super_admin' | 'manager' | 'receptionist' | 'stylist' | 'assistant'\n          phone: string | null\n          hire_date: string | null\n          salary: number | null\n          commission_rate: number | null\n          is_active: boolean | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id: string\n          email: string\n          full_name?: string | null\n          avatar_url?: string | null\n          role?: 'super_admin' | 'manager' | 'receptionist' | 'stylist' | 'assistant'\n          phone?: string | null\n          hire_date?: string | null\n          salary?: number | null\n          commission_rate?: number | null\n          is_active?: boolean | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          email?: string\n          full_name?: string | null\n          avatar_url?: string | null\n          role?: 'super_admin' | 'manager' | 'receptionist' | 'stylist' | 'assistant'\n          phone?: string | null\n          hire_date?: string | null\n          salary?: number | null\n          commission_rate?: number | null\n          is_active?: boolean | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      customers: {\n        Row: {\n          id: string\n          first_name: string\n          last_name: string\n          phone: string\n          email: string | null\n          date_of_birth: string | null\n          gender: 'male' | 'female' | 'other' | null\n          address: string | null\n          notes: string | null\n          avatar_url: string | null\n          tags: string[] | null\n          customer_type: 'vip' | 'regular' | 'potential' | 'lost' | null\n          total_spent: number | null\n          visit_count: number | null\n          last_visit_date: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          first_name: string\n          last_name: string\n          phone: string\n          email?: string | null\n          date_of_birth?: string | null\n          gender?: 'male' | 'female' | 'other' | null\n          address?: string | null\n          notes?: string | null\n          avatar_url?: string | null\n          tags?: string[] | null\n          customer_type?: 'vip' | 'regular' | 'potential' | 'lost' | null\n          total_spent?: number | null\n          visit_count?: number | null\n          last_visit_date?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          first_name?: string\n          last_name?: string\n          phone?: string\n          email?: string | null\n          date_of_birth?: string | null\n          gender?: 'male' | 'female' | 'other' | null\n          address?: string | null\n          notes?: string | null\n          avatar_url?: string | null\n          tags?: string[] | null\n          customer_type?: 'vip' | 'regular' | 'potential' | 'lost' | null\n          total_spent?: number | null\n          visit_count?: number | null\n          last_visit_date?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      services: {\n        Row: {\n          id: string\n          name: string\n          description: string | null\n          duration: number\n          price: number\n          category: 'haircut' | 'styling' | 'treatment' | 'coloring' | 'other'\n          is_active: boolean | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          name: string\n          description?: string | null\n          duration: number\n          price: number\n          category: 'haircut' | 'styling' | 'treatment' | 'coloring' | 'other'\n          is_active?: boolean | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          name?: string\n          description?: string | null\n          duration?: number\n          price?: number\n          category?: 'haircut' | 'styling' | 'treatment' | 'coloring' | 'other'\n          is_active?: boolean | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n      appointments: {\n        Row: {\n          id: string\n          customer_id: string | null\n          staff_id: string | null\n          service_id: string | null\n          appointment_date: string\n          duration_minutes: number | null\n          status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'no_show' | null\n          notes: string | null\n          price: number | null\n          actual_start_time: string | null\n          actual_end_time: string | null\n          created_by: string | null\n          created_at: string\n          updated_at: string\n        }\n        Insert: {\n          id?: string\n          customer_id?: string | null\n          staff_id?: string | null\n          service_id?: string | null\n          appointment_date: string\n          duration_minutes?: number | null\n          status?: 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'no_show' | null\n          notes?: string | null\n          price?: number | null\n          actual_start_time?: string | null\n          actual_end_time?: string | null\n          created_by?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n        Update: {\n          id?: string\n          customer_id?: string | null\n          staff_id?: string | null\n          service_id?: string | null\n          appointment_date?: string\n          duration_minutes?: number | null\n          status?: 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'no_show' | null\n          notes?: string | null\n          price?: number | null\n          actual_start_time?: string | null\n          actual_end_time?: string | null\n          created_by?: string | null\n          created_at?: string\n          updated_at?: string\n        }\n      }\n    }\n    Views: {\n      [_ in never]: never\n    }\n    Functions: {\n      [_ in never]: never\n    }\n    Enums: {\n      user_role: 'super_admin' | 'manager' | 'receptionist' | 'stylist' | 'assistant'\n      customer_type: 'vip' | 'regular' | 'potential' | 'lost'\n      gender: 'male' | 'female' | 'other'\n      appointment_status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'no_show'\n      payment_method: 'cash' | 'card' | 'mobile_pay' | 'member_card'\n      transaction_type: 'income' | 'expense'\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM,cAAc,gFAAwC;AAC5D,MAAM,kBAAkB,wPAA6C;AAG9D,MAAM,WAAW,CAAA,GAAA,uLAAA,CAAA,eAAY,AAAD,EAAE,aAAa;AAG3C,MAAM,uBAAuB,IAAM,CAAA,GAAA,wKAAA,CAAA,8BAA2B,AAAD", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo3/salon-management/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState } from 'react'\nimport { User, Session } from '@supabase/supabase-js'\nimport { createSupabaseClient } from '@/lib/supabase'\n\ninterface AuthContextType {\n  user: User | null\n  session: Session | null\n  loading: boolean\n  signIn: (email: string, password: string) => Promise<{ error: any }>\n  signUp: (email: string, password: string, fullName: string) => Promise<{ error: any }>\n  signOut: () => Promise<void>\n  resetPassword: (email: string) => Promise<{ error: any }>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function AuthProvider({ children }: { children: React.ReactNode }) {\n  const [user, setUser] = useState<User | null>(null)\n  const [session, setSession] = useState<Session | null>(null)\n  const [loading, setLoading] = useState(true)\n  const supabase = createSupabaseClient()\n\n  useEffect(() => {\n    // 获取初始会话\n    const getInitialSession = async () => {\n      const { data: { session } } = await supabase.auth.getSession()\n      setSession(session)\n      setUser(session?.user ?? null)\n      setLoading(false)\n    }\n\n    getInitialSession()\n\n    // 监听认证状态变化\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      async (event, session) => {\n        setSession(session)\n        setUser(session?.user ?? null)\n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [supabase.auth])\n\n  const signIn = async (email: string, password: string) => {\n    const { error } = await supabase.auth.signInWithPassword({\n      email,\n      password,\n    })\n    return { error }\n  }\n\n  const signUp = async (email: string, password: string, fullName: string) => {\n    const { error } = await supabase.auth.signUp({\n      email,\n      password,\n      options: {\n        data: {\n          full_name: fullName,\n        },\n      },\n    })\n    return { error }\n  }\n\n  const signOut = async () => {\n    await supabase.auth.signOut()\n  }\n\n  const resetPassword = async (email: string) => {\n    const { error } = await supabase.auth.resetPasswordForEmail(email, {\n      redirectTo: `${window.location.origin}/reset-password`,\n    })\n    return { error }\n  }\n\n  const value = {\n    user,\n    session,\n    loading,\n    signIn,\n    signUp,\n    signOut,\n    resetPassword,\n  }\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>\n}\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAJA;;;;AAgBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACvD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,WAAW,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD;IAEpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;QACT,MAAM,oBAAoB;YACxB,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;YAC5D,WAAW;YACX,QAAQ,SAAS,QAAQ;YACzB,WAAW;QACb;QAEA;QAEA,WAAW;QACX,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,SAAS,IAAI,CAAC,iBAAiB,CAChE,OAAO,OAAO;YACZ,WAAW;YACX,QAAQ,SAAS,QAAQ;YACzB,WAAW;QACb;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG;QAAC,SAAS,IAAI;KAAC;IAElB,MAAM,SAAS,OAAO,OAAe;QACnC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC;YACvD;YACA;QACF;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;YAC3C;YACA;YACA,SAAS;gBACP,MAAM;oBACJ,WAAW;gBACb;YACF;QACF;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,UAAU;QACd,MAAM,SAAS,IAAI,CAAC,OAAO;IAC7B;IAEA,MAAM,gBAAgB,OAAO;QAC3B,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,qBAAqB,CAAC,OAAO;YACjE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,eAAe,CAAC;QACxD;QACA,OAAO;YAAE;QAAM;IACjB;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 222, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo3/salon-management/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 238, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo3/salon-management/src/components/ui/toast.tsx"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\nimport { X, CheckCircle, AlertCircle, Info, AlertTriangle } from 'lucide-react'\nimport { cn } from '@/lib/utils'\n\nexport interface ToastProps {\n  id: string\n  title?: string\n  description?: string\n  type?: 'success' | 'error' | 'warning' | 'info'\n  duration?: number\n  onClose?: () => void\n}\n\nconst toastVariants = {\n  success: {\n    icon: CheckCircle,\n    className: 'bg-green-50 border-green-200 text-green-900',\n    iconClassName: 'text-green-600'\n  },\n  error: {\n    icon: AlertCircle,\n    className: 'bg-red-50 border-red-200 text-red-900',\n    iconClassName: 'text-red-600'\n  },\n  warning: {\n    icon: AlertTriangle,\n    className: 'bg-yellow-50 border-yellow-200 text-yellow-900',\n    iconClassName: 'text-yellow-600'\n  },\n  info: {\n    icon: Info,\n    className: 'bg-blue-50 border-blue-200 text-blue-900',\n    iconClassName: 'text-blue-600'\n  }\n}\n\nexport function Toast({ \n  id, \n  title, \n  description, \n  type = 'info', \n  duration = 5000, \n  onClose \n}: ToastProps) {\n  const variant = toastVariants[type]\n  const Icon = variant.icon\n\n  React.useEffect(() => {\n    if (duration > 0) {\n      const timer = setTimeout(() => {\n        onClose?.()\n      }, duration)\n      return () => clearTimeout(timer)\n    }\n  }, [duration, onClose])\n\n  return (\n    <div\n      className={cn(\n        'relative flex items-start space-x-3 p-4 border rounded-lg shadow-lg backdrop-blur-sm',\n        'animate-in slide-in-from-right-full duration-300',\n        variant.className\n      )}\n    >\n      <Icon className={cn('h-5 w-5 mt-0.5 flex-shrink-0', variant.iconClassName)} />\n      <div className=\"flex-1 min-w-0\">\n        {title && (\n          <p className=\"text-sm font-semibold\">{title}</p>\n        )}\n        {description && (\n          <p className={cn('text-sm', title ? 'mt-1' : '')}>{description}</p>\n        )}\n      </div>\n      <button\n        onClick={onClose}\n        className=\"flex-shrink-0 p-1 rounded-md hover:bg-black/5 transition-colors\"\n      >\n        <X className=\"h-4 w-4\" />\n      </button>\n    </div>\n  )\n}\n\ninterface ToastContextType {\n  toasts: ToastProps[]\n  addToast: (toast: Omit<ToastProps, 'id'>) => void\n  removeToast: (id: string) => void\n}\n\nconst ToastContext = React.createContext<ToastContextType | undefined>(undefined)\n\nexport function ToastProvider({ children }: { children: React.ReactNode }) {\n  const [toasts, setToasts] = React.useState<ToastProps[]>([])\n\n  const addToast = React.useCallback((toast: Omit<ToastProps, 'id'>) => {\n    const id = Math.random().toString(36).substr(2, 9)\n    setToasts(prev => [...prev, { ...toast, id }])\n  }, [])\n\n  const removeToast = React.useCallback((id: string) => {\n    setToasts(prev => prev.filter(toast => toast.id !== id))\n  }, [])\n\n  return (\n    <ToastContext.Provider value={{ toasts, addToast, removeToast }}>\n      {children}\n      <div className=\"fixed top-4 right-4 z-50 space-y-2 max-w-sm w-full\">\n        {toasts.map(toast => (\n          <Toast\n            key={toast.id}\n            {...toast}\n            onClose={() => removeToast(toast.id)}\n          />\n        ))}\n      </div>\n    </ToastContext.Provider>\n  )\n}\n\nexport function useToast() {\n  const context = React.useContext(ToastContext)\n  if (!context) {\n    throw new Error('useToast must be used within a ToastProvider')\n  }\n  return context\n}\n\n// 便捷方法\nexport function useToastHelpers() {\n  const { addToast } = useToast()\n\n  return {\n    success: (title: string, description?: string) => \n      addToast({ type: 'success', title, description }),\n    error: (title: string, description?: string) => \n      addToast({ type: 'error', title, description }),\n    warning: (title: string, description?: string) => \n      addToast({ type: 'warning', title, description }),\n    info: (title: string, description?: string) => \n      addToast({ type: 'info', title, description })\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAeA,MAAM,gBAAgB;IACpB,SAAS;QACP,MAAM,2NAAA,CAAA,cAAW;QACjB,WAAW;QACX,eAAe;IACjB;IACA,OAAO;QACL,MAAM,oNAAA,CAAA,cAAW;QACjB,WAAW;QACX,eAAe;IACjB;IACA,SAAS;QACP,MAAM,wNAAA,CAAA,gBAAa;QACnB,WAAW;QACX,eAAe;IACjB;IACA,MAAM;QACJ,MAAM,kMAAA,CAAA,OAAI;QACV,WAAW;QACX,eAAe;IACjB;AACF;AAEO,SAAS,MAAM,EACpB,EAAE,EACF,KAAK,EACL,WAAW,EACX,OAAO,MAAM,EACb,WAAW,IAAI,EACf,OAAO,EACI;IACX,MAAM,UAAU,aAAa,CAAC,KAAK;IACnC,MAAM,OAAO,QAAQ,IAAI;IAEzB,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,IAAI,WAAW,GAAG;YAChB,MAAM,QAAQ,WAAW;gBACvB;YACF,GAAG;YACH,OAAO,IAAM,aAAa;QAC5B;IACF,GAAG;QAAC;QAAU;KAAQ;IAEtB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wFACA,oDACA,QAAQ,SAAS;;0BAGnB,8OAAC;gBAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC,QAAQ,aAAa;;;;;;0BACzE,8OAAC;gBAAI,WAAU;;oBACZ,uBACC,8OAAC;wBAAE,WAAU;kCAAyB;;;;;;oBAEvC,6BACC,8OAAC;wBAAE,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,WAAW,QAAQ,SAAS;kCAAM;;;;;;;;;;;;0BAGvD,8OAAC;gBACC,SAAS;gBACT,WAAU;0BAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAIrB;AAQA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EAAgC;AAEhE,SAAS,cAAc,EAAE,QAAQ,EAAiC;IACvE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAgB,EAAE;IAE3D,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE,CAAC;QAClC,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QAChD,UAAU,CAAA,OAAQ;mBAAI;gBAAM;oBAAE,GAAG,KAAK;oBAAE;gBAAG;aAAE;IAC/C,GAAG,EAAE;IAEL,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE,CAAC;QACrC,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD,GAAG,EAAE;IAEL,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAQ;YAAU;QAAY;;YAC3D;0BACD,8OAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAA,sBACV,8OAAC;wBAEE,GAAG,KAAK;wBACT,SAAS,IAAM,YAAY,MAAM,EAAE;uBAF9B,MAAM,EAAE;;;;;;;;;;;;;;;;AAQzB;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAGO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErB,OAAO;QACL,SAAS,CAAC,OAAe,cACvB,SAAS;gBAAE,MAAM;gBAAW;gBAAO;YAAY;QACjD,OAAO,CAAC,OAAe,cACrB,SAAS;gBAAE,MAAM;gBAAS;gBAAO;YAAY;QAC/C,SAAS,CAAC,OAAe,cACvB,SAAS;gBAAE,MAAM;gBAAW;gBAAO;YAAY;QACjD,MAAM,CAAC,OAAe,cACpB,SAAS;gBAAE,MAAM;gBAAQ;gBAAO;YAAY;IAChD;AACF", "debugId": null}}, {"offset": {"line": 434, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo3/salon-management/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 491, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo3/salon-management/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 588, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo3/salon-management/src/components/ui/error-boundary.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Button } from '@/components/ui/button'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { AlertTriangle, RefreshCw } from 'lucide-react'\n\ninterface ErrorBoundaryState {\n  hasError: boolean\n  error?: Error\n}\n\ninterface ErrorBoundaryProps {\n  children: React.ReactNode\n  fallback?: React.ComponentType<{ error?: Error; resetError: () => void }>\n}\n\nexport class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props)\n    this.state = { hasError: false }\n  }\n\n  static getDerivedStateFromError(error: Error): ErrorBoundaryState {\n    return { hasError: true, error }\n  }\n\n  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {\n    console.error('Error caught by boundary:', error, errorInfo)\n  }\n\n  resetError = () => {\n    this.setState({ hasError: false, error: undefined })\n  }\n\n  render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        const FallbackComponent = this.props.fallback\n        return <FallbackComponent error={this.state.error} resetError={this.resetError} />\n      }\n\n      return <DefaultErrorFallback error={this.state.error} resetError={this.resetError} />\n    }\n\n    return this.props.children\n  }\n}\n\nfunction DefaultErrorFallback({ error, resetError }: { error?: Error; resetError: () => void }) {\n  return (\n    <div className=\"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 to-white p-4\">\n      <Card className=\"w-full max-w-md\">\n        <CardHeader className=\"text-center\">\n          <div className=\"w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\">\n            <AlertTriangle className=\"h-8 w-8 text-red-600\" />\n          </div>\n          <CardTitle className=\"text-red-900\">出现错误</CardTitle>\n          <CardDescription>\n            系统遇到了一个意外错误，请尝试刷新页面\n          </CardDescription>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          {error && (\n            <div className=\"p-3 bg-red-50 border border-red-200 rounded-md\">\n              <p className=\"text-sm text-red-800 font-mono\">\n                {error.message}\n              </p>\n            </div>\n          )}\n          <div className=\"flex space-x-2\">\n            <Button onClick={resetError} className=\"flex-1\">\n              <RefreshCw className=\"mr-2 h-4 w-4\" />\n              重试\n            </Button>\n            <Button \n              variant=\"outline\" \n              onClick={() => window.location.reload()}\n              className=\"flex-1\"\n            >\n              刷新页面\n            </Button>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n\nexport function PageErrorFallback({ error, resetError }: { error?: Error; resetError: () => void }) {\n  return (\n    <div className=\"flex items-center justify-center py-12\">\n      <Card className=\"w-full max-w-md\">\n        <CardHeader className=\"text-center\">\n          <div className=\"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-3\">\n            <AlertTriangle className=\"h-6 w-6 text-red-600\" />\n          </div>\n          <CardTitle className=\"text-lg text-red-900\">加载失败</CardTitle>\n          <CardDescription>\n            页面加载时出现错误\n          </CardDescription>\n        </CardHeader>\n        <CardContent>\n          <Button onClick={resetError} className=\"w-full\">\n            <RefreshCw className=\"mr-2 h-4 w-4\" />\n            重新加载\n          </Button>\n        </CardContent>\n      </Card>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AALA;;;;;;AAiBO,MAAM,sBAAsB,qMAAA,CAAA,UAAK,CAAC,SAAS;IAChD,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,yBAAyB,KAAY,EAAsB;QAChE,OAAO;YAAE,UAAU;YAAM;QAAM;IACjC;IAEA,kBAAkB,KAAY,EAAE,SAA0B,EAAE;QAC1D,QAAQ,KAAK,CAAC,6BAA6B,OAAO;IACpD;IAEA,aAAa;QACX,IAAI,CAAC,QAAQ,CAAC;YAAE,UAAU;YAAO,OAAO;QAAU;IACpD,EAAC;IAED,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,MAAM,oBAAoB,IAAI,CAAC,KAAK,CAAC,QAAQ;gBAC7C,qBAAO,8OAAC;oBAAkB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;oBAAE,YAAY,IAAI,CAAC,UAAU;;;;;;YAChF;YAEA,qBAAO,8OAAC;gBAAqB,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;gBAAE,YAAY,IAAI,CAAC,UAAU;;;;;;QACnF;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAEA,SAAS,qBAAqB,EAAE,KAAK,EAAE,UAAU,EAA6C;IAC5F,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;sCAE3B,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAe;;;;;;sCACpC,8OAAC,gIAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;;8BAInB,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;wBACpB,uBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CACV,MAAM,OAAO;;;;;;;;;;;sCAIpB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAS;oCAAY,WAAU;;sDACrC,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGxC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,OAAO,QAAQ,CAAC,MAAM;oCACrC,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;AAEO,SAAS,kBAAkB,EAAE,KAAK,EAAE,UAAU,EAA6C;IAChG,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;;;;;;sCAE3B,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;sCAAuB;;;;;;sCAC5C,8OAAC,gIAAA,CAAA,kBAAe;sCAAC;;;;;;;;;;;;8BAInB,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAY,WAAU;;0CACrC,8OAAC,gNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;;;;;AAOlD", "debugId": null}}]}