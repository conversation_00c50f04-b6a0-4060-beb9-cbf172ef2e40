{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/demo3/salon-management/src/app/api/dashboard/stats/route.ts"], "sourcesContent": ["import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'\nimport { cookies } from 'next/headers'\nimport { NextRequest, NextResponse } from 'next/server'\n\n// GET /api/dashboard/stats - 获取仪表板统计数据\nexport async function GET(request: NextRequest) {\n  try {\n    const supabase = createRouteHandlerClient({ cookies })\n    \n    // 验证用户是否已登录\n    const { data: { session } } = await supabase.auth.getSession()\n    if (!session) {\n      return NextResponse.json({ error: '未授权访问' }, { status: 401 })\n    }\n\n    // 调用数据库函数获取统计数据\n    const { data, error } = await supabase.rpc('get_dashboard_stats')\n\n    if (error) {\n      console.error('获取统计数据失败:', error)\n      return NextResponse.json({ error: '获取统计数据失败' }, { status: 500 })\n    }\n\n    return NextResponse.json({ data })\n  } catch (error) {\n    console.error('API错误:', error)\n    return NextResponse.json({ error: '服务器内部错误' }, { status: 500 })\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,WAAW,CAAA,GAAA,0KAAA,CAAA,2BAAwB,AAAD,EAAE;YAAE,SAAA,iIAAA,CAAA,UAAO;QAAC;QAEpD,YAAY;QACZ,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,MAAM,SAAS,IAAI,CAAC,UAAU;QAC5D,IAAI,CAAC,SAAS;YACZ,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAQ,GAAG;gBAAE,QAAQ;YAAI;QAC7D;QAEA,gBAAgB;QAChB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,SAAS,GAAG,CAAC;QAE3C,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,aAAa;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAW,GAAG;gBAAE,QAAQ;YAAI;QAChE;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAK;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,UAAU;QACxB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAU,GAAG;YAAE,QAAQ;QAAI;IAC/D;AACF", "debugId": null}}]}