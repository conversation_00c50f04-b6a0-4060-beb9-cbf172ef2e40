{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_8c25f929._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_d459077f.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/dashboard/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/login(\\\\.json)?[\\/#\\?]?$", "originalSource": "/login"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/register(\\\\.json)?[\\/#\\?]?$", "originalSource": "/register"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "rNOPXVBvGRtc5JVe6ijf6XSTsrELRhLz1mhvGGhkKPE=", "__NEXT_PREVIEW_MODE_ID": "2543326f4251957f04c1dad2438b6ba6", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "25806364710f53e7c9a0b782d882a9aaae219134651ed586465aeee82b982396", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a37f598948efaac6903222735fde6859be24ee2915f8d8117d3128e59cae2cf5"}}}, "sortedMiddleware": ["/"], "functions": {}}