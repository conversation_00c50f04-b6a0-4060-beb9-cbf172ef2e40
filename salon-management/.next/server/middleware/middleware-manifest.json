{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_8c25f929._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_d459077f.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/dashboard/:path*{(\\\\.json)}?", "originalSource": "/dashboard/:path*"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/login{(\\\\.json)}?", "originalSource": "/login"}, {"regexp": "/:nextData(_next/data/[^/]{1,})?/register{(\\\\.json)}?", "originalSource": "/register"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "rNOPXVBvGRtc5JVe6ijf6XSTsrELRhLz1mhvGGhkKPE=", "__NEXT_PREVIEW_MODE_ID": "e420990d4567ff0f5f7f7cde22a5e408", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9c6843ebea595bd4eedc9e861222501f02c26ef9c218d693c7752cf4ef153884", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "e26535fc10167fa4b36199a198a9f23bb8ada11b46390f7e6603ddf21e5b84d7"}}}, "instrumentation": null, "functions": {}}